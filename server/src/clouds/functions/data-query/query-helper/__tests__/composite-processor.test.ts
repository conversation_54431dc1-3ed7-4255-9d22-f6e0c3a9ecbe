/**
 * 复合指标处理器测试
 */

import { CompositeMetricProcessor } from '../composite-processor'
import { QueryContext } from '../query-interfaces'

describe('CompositeMetricProcessor', () => {
  let processor: CompositeMetricProcessor

  beforeEach(() => {
    processor = new CompositeMetricProcessor()
  })

  describe('复合指标类型检测', () => {
    test('应该正确检测纯实时复合指标', () => {
      const pureRealtimeInfo = {
        baseIndiceId: 'composite1',
        sqlModelId: 'complex_composite1',
        composedMetricInfos: [
          {
            baseInfo: { isLanding: false, type: 'base', code: 'metric1' },
            baseIndicesCode: 'metric1'
          },
          {
            baseInfo: { isLanding: false, type: 'base', code: 'metric2' },
            baseIndicesCode: 'metric2'
          }
        ]
      }

      const type = processor.detectCompositeType(pureRealtimeInfo as any)
      expect(type).toBe('pure_realtime')
    })

    test('应该正确检测混合复合指标', () => {
      const mixedInfo = {
        baseIndiceId: 'composite2',
        sqlModelId: 'complex_composite2',
        composedMetricInfos: [
          {
            baseInfo: { isLanding: false, type: 'base', code: 'realtime_metric' },
            baseIndicesCode: 'realtime_metric'
          },
          {
            baseInfo: { isLanding: true, type: 'base', code: 'mindex_metric' },
            baseIndicesCode: 'mindex_metric'
          }
        ]
      }

      const type = processor.detectCompositeType(mixedInfo as any)
      expect(type).toBe('mixed')
    })

    test('应该正确检测数仓复合指标', () => {
      const datawarehouseInfo = {
        baseIndiceId: 'composite3',
        sqlModelId: 'dw_model1',
        databaseId: 'dw_db1',
        sql: 'SELECT * FROM dw_table',
        composedMetricInfos: [
          {
            baseInfo: { type: 'base', code: 'dw_metric1' },
            baseIndicesCode: 'dw_metric1',
            sqlModelId: 'dw_model1'
          }
        ]
      }

      const type = processor.detectCompositeType(datawarehouseInfo as any)
      expect(type).toBe('datawarehouse')
    })

    test('应该处理非复合指标', () => {
      const regularInfo = {
        baseIndiceId: 'regular1',
        sqlModelId: 'realtime'
      }

      const type = processor.detectCompositeType(regularInfo as any)
      expect(type).toBe('none')
    })
  })

  describe('复合指标处理', () => {
    test('应该处理纯实时复合指标', async () => {
      const pureRealtimeInfo = {
        baseIndiceId: 'composite1',
        sqlModelId: 'complex_composite1',
        composedMetricInfos: [
          {
            baseInfo: { isLanding: false, type: 'base', code: 'metric1' },
            baseIndicesCode: 'metric1'
          },
          {
            baseInfo: { isLanding: false, type: 'base', code: 'metric2' },
            baseIndicesCode: 'metric2'
          }
        ]
      }

      const mockContext: QueryContext = {
        queryConfig: {
          type: 'indicesTable',
          fieldsBinding: {},
          filters: [],
          timeBucket: 'DAY'
        },
        req: {
          masterRequester: {
            get: jest.fn().mockResolvedValue({ data: [] }),
            post: jest.fn().mockResolvedValue({ data: [] })
          } as any
        },
        db: {},
        infos: [pureRealtimeInfo as any],
        startTime: Date.now()
      }

      // Mock queryRealtimeIndicesTable
      jest.doMock('../../realtime-indices', () => ({
        queryRealtimeIndicesTable: jest.fn().mockResolvedValue([
          { metric1: 100, metric2: 200, dim1: 'test' }
        ])
      }))

      const results = await processor.processComposite(pureRealtimeInfo as any, mockContext)

      expect(results).toHaveLength(1)
      expect(results[0].metadata?.compositeType).toBe('pure_realtime')
      expect(results[0].metadata?.isComposite).toBe(true)
    })

    test('应该处理混合复合指标', async () => {
      const mixedInfo = {
        baseIndiceId: 'composite2',
        sqlModelId: 'complex_composite2',
        composedMetricInfos: [
          {
            baseInfo: { isLanding: false, type: 'base', code: 'realtime_metric' },
            baseIndicesCode: 'realtime_metric'
          },
          {
            baseInfo: { isLanding: true, type: 'base', code: 'mindex_metric' },
            baseIndicesCode: 'mindex_metric'
          }
        ]
      }

      const mockContext: QueryContext = {
        queryConfig: {
          type: 'indicesTable',
          fieldsBinding: {},
          filters: [],
          timeBucket: 'DAY'
        },
        req: {
          masterRequester: {
            get: jest.fn().mockResolvedValue({ data: [] }),
            post: jest.fn().mockResolvedValue({ data: [] })
          } as any
        },
        db: {},
        infos: [mixedInfo as any],
        startTime: Date.now()
      }

      // Mock both query functions
      jest.doMock('../../realtime-indices', () => ({
        queryRealtimeIndicesTable: jest.fn().mockResolvedValue([
          { realtime_metric: 100, dim1: 'test' }
        ])
      }))

      jest.doMock('../../tindex', () => ({
        queryIndicatorData: jest.fn().mockResolvedValue([
          { mindex_metric: 200, dim1: 'test' }
        ])
      }))

      const results = await processor.processComposite(mixedInfo as any, mockContext)

      expect(results).toHaveLength(1)
      expect(results[0].metadata?.compositeType).toBe('mixed')
      expect(results[0].metadata?.isComposite).toBe(true)
    })
  })

  describe('依赖关系验证', () => {
    test('应该验证复合指标的依赖关系', () => {
      const compositeInfo = {
        baseIndiceId: 'composite1',
        composedMetricInfos: [
          {
            baseIndicesCode: 'metric1',
            baseInfo: { code: 'metric1' }
          },
          {
            baseIndicesCode: 'metric2',
            baseInfo: { code: 'metric2' }
          }
        ]
      }

      const isValid = processor.validateDependencies(compositeInfo as any)
      expect(isValid).toBe(true)
    })

    test('应该检测循环依赖', () => {
      const circularInfo = {
        baseIndiceId: 'composite1',
        composedMetricInfos: [
          {
            baseIndicesCode: 'composite1', // 自引用
            baseInfo: { code: 'composite1' }
          }
        ]
      }

      const isValid = processor.validateDependencies(circularInfo as any)
      expect(isValid).toBe(false)
    })

    test('应该检测缺失的依赖', () => {
      const missingDepsInfo = {
        baseIndiceId: 'composite1',
        composedMetricInfos: [
          {
            baseIndicesCode: 'missing_metric',
            baseInfo: null // 缺失的依赖
          }
        ]
      }

      const isValid = processor.validateDependencies(missingDepsInfo as any)
      expect(isValid).toBe(false)
    })
  })

  describe('静态方法保留验证', () => {
    test('应该保留getCompositeFormulaWithCode静态方法', () => {
      expect(typeof CompositeMetricProcessor.getCompositeFormulaWithCode).toBe('function')
    })

    test('应该保留resolveComplexColumn静态方法', () => {
      expect(typeof CompositeMetricProcessor.resolveComplexColumn).toBe('function')
    })

    test('应该保留isPureRealtimeComposite静态方法', () => {
      expect(typeof CompositeMetricProcessor.isPureRealtimeComposite).toBe('function')
    })

    test('getCompositeFormulaWithCode应该正确处理复合公式', () => {
      const mockInfo = {
        composedSQLFormula: '$metric1 + $metric2',
        composedMetricInfos: [
          { baseIndicesCode: 'metric1' },
          { baseIndicesCode: 'metric2' }
        ]
      }

      const mockFieldsBinding = {
        'metric1': { name: 'metric1' },
        'metric2': { name: 'metric2' }
      }

      const result = CompositeMetricProcessor.getCompositeFormulaWithCode(
        mockInfo as any,
        mockFieldsBinding as any
      )

      expect(result).toContain('metric1')
      expect(result).toContain('metric2')
    })

    test('isPureRealtimeComposite应该正确识别纯实时复合指标', () => {
      const pureRealtimeInfo = {
        composedMetricInfos: [
          { baseInfo: { isLanding: false } },
          { baseInfo: { isLanding: false } }
        ]
      }

      const result = CompositeMetricProcessor.isPureRealtimeComposite(pureRealtimeInfo as any)
      expect(result).toBe(true)
    })

    test('isPureRealtimeComposite应该正确识别非纯实时复合指标', () => {
      const mixedInfo = {
        composedMetricInfos: [
          { baseInfo: { isLanding: false } },
          { baseInfo: { isLanding: true } }
        ]
      }

      const result = CompositeMetricProcessor.isPureRealtimeComposite(mixedInfo as any)
      expect(result).toBe(false)
    })
  })
})
