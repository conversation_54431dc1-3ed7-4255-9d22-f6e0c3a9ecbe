/**
 * 查询分发器测试
 */

import { QueryDispatcher } from '../query-dispatcher'
import { QueryHandler, QueryContext, QueryResult, QueryType } from '../query-interfaces'

// Mock查询处理器
class MockRealtimeHandler implements QueryHandler {
  canHandle(infos: any[]): boolean {
    return infos.some(info => info.sqlModelId === 'realtime')
  }

  async execute(_context: QueryContext): Promise<QueryResult[]> {
    return [{
      data: [{ metric1: 100, dim1: 'test' }],
      metadata: { queryType: 'realtime', dataSource: 'db1' },
      performance: { executionTime: 50, rowCount: 1, metricCount: 1, optimized: true }
    }]
  }

  getQueryType(): string {
    return QueryType.REALTIME
  }

  getPriority(): number {
    return 100
  }
}

class MockDatawarehouseHandler implements QueryHandler {
  canHandle(infos: any[]): boolean {
    return infos.some(info => info.sqlModelId && info.sqlModelId !== 'realtime')
  }

  async execute(_context: QueryContext): Promise<QueryResult[]> {
    return [{
      data: [{ metric2: 200, dim1: 'test' }],
      metadata: { queryType: 'datawarehouse', dataSource: 'db2' },
      performance: { executionTime: 80, rowCount: 1, metricCount: 1, optimized: false }
    }]
  }

  getQueryType(): string {
    return QueryType.DATAWAREHOUSE
  }

  getPriority(): number {
    return 80
  }
}

describe('QueryDispatcher', () => {
  let dispatcher: QueryDispatcher
  let mockRealtimeHandler: MockRealtimeHandler
  let mockDatawarehouseHandler: MockDatawarehouseHandler

  beforeEach(() => {
    dispatcher = new QueryDispatcher()
    mockRealtimeHandler = new MockRealtimeHandler()
    mockDatawarehouseHandler = new MockDatawarehouseHandler()
  })

  afterEach(() => {
    dispatcher.clearHandlers()
  })

  describe('处理器注册', () => {
    test('应该能够注册查询处理器', () => {
      dispatcher.registerHandler(mockRealtimeHandler)

      const handlers = dispatcher.getRegisteredHandlers()
      expect(handlers).toContain('realtime')
    })

    test('应该能够注册多个查询处理器', () => {
      dispatcher.registerHandler(mockRealtimeHandler)
      dispatcher.registerHandler(mockDatawarehouseHandler)

      const handlers = dispatcher.getRegisteredHandlers()
      expect(handlers).toContain('realtime')
      expect(handlers).toContain('datawarehouse')
      expect(handlers).toHaveLength(2)
    })

    test('应该能够清除所有处理器', () => {
      dispatcher.registerHandler(mockRealtimeHandler)
      dispatcher.registerHandler(mockDatawarehouseHandler)

      dispatcher.clearHandlers()

      const handlers = dispatcher.getRegisteredHandlers()
      expect(handlers).toHaveLength(0)
    })
  })

  describe('处理器选择', () => {
    beforeEach(() => {
      dispatcher.registerHandler(mockRealtimeHandler)
      dispatcher.registerHandler(mockDatawarehouseHandler)
    })

    test('应该根据优先级选择处理器', () => {
      const realtimeInfos = [{ sqlModelId: 'realtime', baseIndiceId: 'metric1' }]
      const handler = dispatcher.getHandler(realtimeInfos)

      expect(handler).toBe(mockRealtimeHandler)
      expect(handler?.getQueryType()).toBe('realtime')
    })

    test('应该为数仓指标选择正确的处理器', () => {
      const datawarehouseInfos = [{ sqlModelId: 'model1', baseIndiceId: 'metric2' }]
      const handler = dispatcher.getHandler(datawarehouseInfos)

      expect(handler).toBe(mockDatawarehouseHandler)
      expect(handler?.getQueryType()).toBe('datawarehouse')
    })

    test('当没有合适的处理器时应该返回null', () => {
      const unknownInfos = [{ sqlModelId: 'unknown', baseIndiceId: 'metric3' }]
      const handler = dispatcher.getHandler(unknownInfos as any)

      expect(handler).toBeNull()
    })
  })

  describe('查询分发', () => {
    beforeEach(() => {
      dispatcher.registerHandler(mockRealtimeHandler)
      dispatcher.registerHandler(mockDatawarehouseHandler)
    })

    test('应该正确分发rawSql查询', async () => {
      const queryConfig = { type: 'rawSql', sql: 'SELECT * FROM test' }
      const mockReq = {
        masterRequester: {
          get: jest.fn().mockResolvedValue({ data: [] }),
          post: jest.fn().mockResolvedValue({ data: [] })
        } as any
      }
      const mockDb = {}

      // 这个测试应该验证分发逻辑，而不是具体的查询执行
      // 因为rawSql查询会直接调用原有逻辑，不经过模块化处理器
      const result = await dispatcher.dispatch(queryConfig as any, mockReq as any, mockDb)
      expect(Array.isArray(result)).toBe(true)
    })

    test('应该正确分发dataCenter查询', async () => {
      const queryConfig = { type: 'dataCenter', connId: 'conn1' }
      const mockReq = {
        masterRequester: {
          get: jest.fn().mockResolvedValue({ data: [{ id: 'conn1', type: 'MYSQL' }] }),
          post: jest.fn().mockResolvedValue({ data: [] })
        } as any
      }
      const mockDb = {}

      // 这个测试应该验证分发逻辑，而不是具体的查询执行
      // 因为dataCenter查询会直接调用原有逻辑，不经过模块化处理器
      const result = await dispatcher.dispatch(queryConfig as any, mockReq as any, mockDb)
      expect(Array.isArray(result)).toBe(true)
    })

    test('应该正确分发dataset查询', async () => {
      const queryConfig = { type: 'dataset', datasetId: 'dataset1' }
      const mockReq = {
        masterRequester: {
          get: jest.fn().mockResolvedValue({ data: [] }),
          post: jest.fn().mockResolvedValue({ data: [] })
        } as any
      }
      const mockDb = { Dataset: { findByPk: jest.fn().mockResolvedValue({ dbType: 'MYSQL' }) } }

      // 这个测试应该验证分发逻辑，而不是具体的查询执行
      // 因为dataset查询会直接调用原有逻辑，不经过模块化处理器
      const result = await dispatcher.dispatch(queryConfig as any, mockReq as any, mockDb)
      expect(Array.isArray(result)).toBe(true)
    })
  })

  describe('错误处理', () => {
    test('应该处理查询执行错误', async () => {
      const errorHandler = {
        canHandle: () => true,
        execute: () => Promise.reject(new Error('查询失败')),
        getQueryType: () => 'error',
        getPriority: () => 50
      }

      dispatcher.registerHandler(errorHandler as any)

      const queryConfig = { type: 'indicesTable' }
      const mockReq = {
        masterRequester: {
          get: jest.fn().mockResolvedValue({ data: [] }),
          post: jest.fn().mockResolvedValue({ data: [] })
        } as any
      }
      const mockDb = {}

      // 由于模块化查询失败会回退到原有逻辑，我们测试这种情况
      await expect(dispatcher.dispatch(queryConfig as any, mockReq as any, mockDb))
        .rejects.toThrow()
    })
  })
})
