/**
 * 查询处理器测试
 */

import { RealtimeQueryHandler } from '../query-types/realtime-query'
import { DatawarehouseQueryHandler } from '../query-types/datawarehouse-query'
import { MindexQueryHandler } from '../query-types/mindex-query'
import { QueryContext } from '../query-interfaces'

// Mock外部依赖
jest.mock('../../realtime-indices', () => ({
  queryRealtimeIndicesTable: jest.fn().mockResolvedValue([
    { metric1: 100, dim1: 'test' }
  ]),
  getCompositeFormulaWithCode: jest.fn(),
  resolveComplexColumn: jest.fn()
}))

jest.mock('../../sql-mapping-model', () => ({
  querySqlMappingModel: jest.fn().mockResolvedValue([
    { metric2: 200, dim1: 'test' }
  ])
}))

jest.mock('../../tindex', () => ({
  queryIndicatorData: jest.fn().mockResolvedValue([
    { metric3: 300, dim1: 'test' }
  ])
}))

describe('查询处理器', () => {
  let mockContext: QueryContext

  beforeEach(() => {
    mockContext = {
      queryConfig: {
        type: 'indicesTable',
        fieldsBinding: {
          dim1: { id: 'dim1', type: 'indicesDims', name: 'dim1' },
          metric1: { id: 'metric1', type: 'indicesSpec', name: 'metric1' }
        },
        filters: [],
        timeBucket: 'DAY',
        limit: 100
      },
      req: {
        masterRequester: {
          get: jest.fn().mockResolvedValue({ data: [] }),
          post: jest.fn().mockResolvedValue({ data: [] })
        } as any
      },
      db: {},
      infos: [],
      startTime: Date.now()
    }
  })

  describe('RealtimeQueryHandler', () => {
    let handler: RealtimeQueryHandler

    beforeEach(() => {
      handler = new RealtimeQueryHandler()
    })

    test('应该正确识别实时指标', () => {
      const realtimeInfos = [
        {
          baseIndiceId: 'metric1',
          sqlModelId: 'realtime',
          baseInfo: { isLanding: false }
        }
      ]

      const canHandle = handler.canHandle(realtimeInfos as any)
      expect(canHandle).toBe(true)
    })

    test('应该正确识别非实时指标', () => {
      const nonRealtimeInfos = [
        {
          baseIndiceId: 'metric1',
          sqlModelId: 'mindex',
          baseInfo: { isLanding: true }
        }
      ]

      const canHandle = handler.canHandle(nonRealtimeInfos as any)
      expect(canHandle).toBe(false)
    })

    test('应该返回正确的查询类型', () => {
      expect(handler.getQueryType()).toBe('realtime')
    })

    test('应该返回正确的优先级', () => {
      expect(handler.getPriority()).toBe(100)
    })

    test('应该执行实时指标查询', async () => {
      const realtimeInfos = [
        {
          baseIndiceId: 'metric1',
          sqlModelId: 'realtime',
          baseInfo: { isLanding: false }
        }
      ]

      mockContext.infos = realtimeInfos as any

      const results = await handler.execute(mockContext)

      expect(results).toHaveLength(1)
      expect(results[0].metadata?.queryType).toBe('realtime')
      expect(Array.isArray(results[0].data)).toBe(true)
    })

    test('应该处理复合实时指标', async () => {
      const compositeInfo = [
        {
          baseIndiceId: 'composite1',
          sqlModelId: 'complex_composite1',
          composedMetricInfos: [
            {
              baseInfo: { isLanding: false, code: 'metric1' },
              baseIndicesCode: 'metric1'
            },
            {
              baseInfo: { isLanding: false, code: 'metric2' },
              baseIndicesCode: 'metric2'
            }
          ]
        }
      ]

      mockContext.infos = compositeInfo as any

      const results = await handler.execute(mockContext)

      expect(results).toHaveLength(1)
      expect(results[0].metadata?.queryType).toContain('composite')
    })
  })

  describe('DatawarehouseQueryHandler', () => {
    let handler: DatawarehouseQueryHandler

    beforeEach(() => {
      handler = new DatawarehouseQueryHandler()
    })

    test('应该正确识别数仓指标', () => {
      const datawarehouseInfos = [
        {
          baseIndiceId: 'metric1',
          sqlModelId: 'dw_model1',
          databaseId: 'dw_db1',
          sql: 'SELECT * FROM dw_table'
        }
      ]

      const canHandle = handler.canHandle(datawarehouseInfos as any)
      expect(canHandle).toBe(true)
    })

    test('应该正确识别非数仓指标', () => {
      const nonDatawarehouseInfos = [
        {
          baseIndiceId: 'metric1',
          sqlModelId: 'realtime'
        }
      ]

      const canHandle = handler.canHandle(nonDatawarehouseInfos as any)
      expect(canHandle).toBe(false)
    })

    test('应该返回正确的查询类型', () => {
      expect(handler.getQueryType()).toBe('datawarehouse')
    })

    test('应该返回正确的优先级', () => {
      expect(handler.getPriority()).toBe(80)
    })

    test('应该执行数仓指标查询', async () => {
      const datawarehouseInfos = [
        {
          baseIndiceId: 'metric1',
          sqlModelId: 'dw_model1',
          databaseId: 'dw_db1',
          sql: 'SELECT * FROM dw_table'
        }
      ]

      mockContext.infos = datawarehouseInfos as any

      const results = await handler.execute(mockContext)

      expect(results).toHaveLength(1)
      expect(results[0].metadata?.queryType).toBe('datawarehouse')
      expect(Array.isArray(results[0].data)).toBe(true)
    })

    test('应该处理数仓复合指标', async () => {
      const compositeInfo = [
        {
          baseIndiceId: 'composite1',
          sqlModelId: 'dw_model1',
          databaseId: 'dw_db1',
          composedMetricInfos: [
            {
              baseInfo: { code: 'dw_metric1' },
              baseIndicesCode: 'dw_metric1',
              sqlModelId: 'dw_model1'
            }
          ]
        }
      ]

      mockContext.infos = compositeInfo as any

      const results = await handler.execute(mockContext)

      expect(results).toHaveLength(1)
      expect(results[0].metadata?.queryType).toContain('composite')
    })
  })

  describe('MindexQueryHandler', () => {
    let handler: MindexQueryHandler

    beforeEach(() => {
      handler = new MindexQueryHandler()
    })

    test('应该正确识别mindex指标', () => {
      const mindexInfos = [
        {
          baseIndiceId: 'metric1',
          sqlModelId: 'mindex',
          baseInfo: { isLanding: true }
        }
      ]

      const canHandle = handler.canHandle(mindexInfos as any)
      expect(canHandle).toBe(true)
    })

    test('应该正确识别传统指标', () => {
      const traditionalInfos = [
        {
          baseIndiceId: 'metric1',
          baseInfo: { isLanding: true }
        }
      ]

      const canHandle = handler.canHandle(traditionalInfos as any)
      expect(canHandle).toBe(true)
    })

    test('应该正确识别非mindex指标', () => {
      const nonMindexInfos = [
        {
          baseIndiceId: 'metric1',
          sqlModelId: 'realtime',
          baseInfo: { isLanding: false }
        }
      ]

      const canHandle = handler.canHandle(nonMindexInfos as any)
      expect(canHandle).toBe(false)
    })

    test('应该返回正确的查询类型', () => {
      expect(handler.getQueryType()).toBe('mindex')
    })

    test('应该返回正确的优先级', () => {
      expect(handler.getPriority()).toBe(60)
    })

    test('应该执行mindex指标查询', async () => {
      const mindexInfos = [
        {
          baseIndiceId: 'metric1',
          sqlModelId: 'mindex',
          baseInfo: { isLanding: true }
        }
      ]

      mockContext.infos = mindexInfos as any

      const results = await handler.execute(mockContext)

      expect(results).toHaveLength(1)
      expect(results[0].metadata?.queryType).toContain('mindex')
      expect(Array.isArray(results[0].data)).toBe(true)
    })

    test('应该处理批量查询失败的降级', async () => {
      // Mock queryIndicatorData抛出错误
      const { queryIndicatorData } = require('../../tindex')
      queryIndicatorData.mockRejectedValueOnce(new Error('批量查询失败'))

      const mindexInfos = [
        {
          baseIndiceId: 'metric1',
          sqlModelId: 'mindex',
          baseInfo: { isLanding: true }
        },
        {
          baseIndiceId: 'metric2',
          sqlModelId: 'mindex',
          baseInfo: { isLanding: true }
        }
      ]

      mockContext.infos = mindexInfos as any

      const results = await handler.execute(mockContext)

      // 应该返回降级结果，而不是抛出错误
      expect(results).toHaveLength(1)
      expect(results[0].metadata?.queryType).toContain('failed')
    })
  })

  describe('处理器集成测试', () => {
    test('所有处理器应该实现相同的接口', () => {
      const handlers = [
        new RealtimeQueryHandler(),
        new DatawarehouseQueryHandler(),
        new MindexQueryHandler()
      ]

      handlers.forEach(handler => {
        expect(typeof handler.canHandle).toBe('function')
        expect(typeof handler.execute).toBe('function')
        expect(typeof handler.getQueryType).toBe('function')
        expect(typeof handler.getPriority).toBe('function')
      })
    })

    test('处理器优先级应该正确排序', () => {
      const realtimeHandler = new RealtimeQueryHandler()
      const datawarehouseHandler = new DatawarehouseQueryHandler()
      const mindexHandler = new MindexQueryHandler()

      expect(realtimeHandler.getPriority()).toBeGreaterThan(datawarehouseHandler.getPriority())
      expect(datawarehouseHandler.getPriority()).toBeGreaterThan(mindexHandler.getPriority())
    })

    test('处理器应该返回唯一的查询类型', () => {
      const handlers = [
        new RealtimeQueryHandler(),
        new DatawarehouseQueryHandler(),
        new MindexQueryHandler()
      ]

      const queryTypes = handlers.map(h => h.getQueryType())
      const uniqueTypes = [...new Set(queryTypes)]

      expect(uniqueTypes).toHaveLength(queryTypes.length)
    })
  })
})
