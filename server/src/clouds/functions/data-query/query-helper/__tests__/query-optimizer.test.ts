/**
 * 查询优化器测试
 */

import { QueryOptimizer } from '../query-optimizer'
import { DataSourceGroup } from '../query-interfaces'

describe('QueryOptimizer', () => {
  let optimizer: QueryOptimizer

  beforeEach(() => {
    optimizer = new QueryOptimizer()
  })

  describe('数据源分组', () => {
    test('应该按数据源ID分组指标', () => {
      const mockInfos = [
        {
          baseIndiceId: 'metric1',
          databaseId: 'db1',
          sqlModelId: 'realtime',
          baseInfo: { code: 'metric1' }
        },
        {
          baseIndiceId: 'metric2',
          databaseId: 'db1',
          sqlModelId: 'realtime',
          baseInfo: { code: 'metric2' }
        },
        {
          baseIndiceId: 'metric3',
          databaseId: 'db2',
          sqlModelId: 'model1',
          baseInfo: { code: 'metric3' }
        }
      ]

      const groups = optimizer.groupByDataSource(mockInfos as any)
      
      expect(groups).toHaveLength(2)
      
      // 第一组：db1的实时指标
      const db1Group = groups.find(g => g.databaseId === 'db1')
      expect(db1Group).toBeDefined()
      expect(db1Group?.infos).toHaveLength(2)
      expect(db1Group?.groupType).toBe('realtime')
      
      // 第二组：db2的数仓指标
      const db2Group = groups.find(g => g.databaseId === 'db2')
      expect(db2Group).toBeDefined()
      expect(db2Group?.infos).toHaveLength(1)
      expect(db2Group?.groupType).toBe('datawarehouse')
    })

    test('应该正确识别分组类型', () => {
      const realtimeInfos = [
        {
          baseIndiceId: 'metric1',
          databaseId: 'db1',
          sqlModelId: 'realtime',
          baseInfo: { isLanding: false, code: 'metric1' }
        }
      ]

      const mindexInfos = [
        {
          baseIndiceId: 'metric2',
          databaseId: 'db1',
          sqlModelId: 'mindex',
          baseInfo: { isLanding: true, code: 'metric2' }
        }
      ]

      const datawarehouseInfos = [
        {
          baseIndiceId: 'metric3',
          databaseId: 'db1',
          sqlModelId: 'custom_model',
          baseInfo: { code: 'metric3' }
        }
      ]

      const realtimeGroups = optimizer.groupByDataSource(realtimeInfos as any)
      expect(realtimeGroups[0].groupType).toBe('realtime')

      const mindexGroups = optimizer.groupByDataSource(mindexInfos as any)
      expect(mindexGroups[0].groupType).toBe('mindex')

      const datawarehouseGroups = optimizer.groupByDataSource(datawarehouseInfos as any)
      expect(datawarehouseGroups[0].groupType).toBe('datawarehouse')
    })

    test('应该处理混合类型分组', () => {
      const mixedInfos = [
        {
          baseIndiceId: 'metric1',
          databaseId: 'db1',
          sqlModelId: 'realtime',
          baseInfo: { isLanding: false, code: 'metric1' }
        },
        {
          baseIndiceId: 'metric2',
          databaseId: 'db1',
          sqlModelId: 'mindex',
          baseInfo: { isLanding: true, code: 'metric2' }
        }
      ]

      const groups = optimizer.groupByDataSource(mixedInfos as any)
      
      expect(groups).toHaveLength(2) // 应该分成两组
      expect(groups.some(g => g.groupType === 'realtime')).toBe(true)
      expect(groups.some(g => g.groupType === 'mindex')).toBe(true)
    })
  })

  describe('CTE优化判断', () => {
    test('应该为同源多指标启用CTE优化', () => {
      const group: DataSourceGroup = {
        databaseId: 'db1',
        sqlModelId: 'realtime',
        infos: [
          { baseIndiceId: 'metric1', baseInfo: { code: 'metric1' } },
          { baseIndiceId: 'metric2', baseInfo: { code: 'metric2' } }
        ] as any,
        canUseCTE: false,
        groupType: 'realtime'
      }

      const canUseCTE = optimizer.canUseCTEOptimization(group)
      expect(canUseCTE).toBe(true)
    })

    test('应该为单个指标禁用CTE优化', () => {
      const group: DataSourceGroup = {
        databaseId: 'db1',
        sqlModelId: 'realtime',
        infos: [
          { baseIndiceId: 'metric1', baseInfo: { code: 'metric1' } }
        ] as any,
        canUseCTE: false,
        groupType: 'realtime'
      }

      const canUseCTE = optimizer.canUseCTEOptimization(group)
      expect(canUseCTE).toBe(false)
    })

    test('应该为默认数据源禁用CTE优化', () => {
      const group: DataSourceGroup = {
        databaseId: 'default',
        sqlModelId: 'realtime',
        infos: [
          { baseIndiceId: 'metric1', baseInfo: { code: 'metric1' } },
          { baseIndiceId: 'metric2', baseInfo: { code: 'metric2' } }
        ] as any,
        canUseCTE: false,
        groupType: 'realtime'
      }

      const canUseCTE = optimizer.canUseCTEOptimization(group)
      expect(canUseCTE).toBe(false)
    })

    test('应该为同SQL模型的数仓指标启用CTE优化', () => {
      const group: DataSourceGroup = {
        databaseId: 'dw_db1',
        sqlModelId: 'dw_model1',
        infos: [
          { baseIndiceId: 'metric1', sqlModelId: 'dw_model1', baseInfo: { code: 'metric1' } },
          { baseIndiceId: 'metric2', sqlModelId: 'dw_model1', baseInfo: { code: 'metric2' } }
        ] as any,
        canUseCTE: false,
        groupType: 'datawarehouse'
      }

      const canUseCTE = optimizer.canUseCTEOptimization(group)
      expect(canUseCTE).toBe(true)
    })
  })

  describe('SQL优化', () => {
    test('应该为CTE优化生成正确的SQL', () => {
      const group: DataSourceGroup = {
        databaseId: 'db1',
        sqlModelId: 'realtime',
        infos: [
          { baseIndiceId: 'metric1', baseInfo: { code: 'metric1' } },
          { baseIndiceId: 'metric2', baseInfo: { code: 'metric2' } }
        ] as any,
        canUseCTE: true,
        groupType: 'realtime'
      }

      const context = {
        queryConfig: {
          fieldsBinding: {
            dim1: { type: 'indicesDims', name: 'dim1' },
            metric1: { type: 'indicesSpec', name: 'metric1' },
            metric2: { type: 'indicesSpec', name: 'metric2' }
          },
          filters: [],
          timeBucket: 'DAY',
          orderBy: [],
          limit: 100
        }
      }

      const sql = optimizer.optimizeQuery(group, context as any)
      
      expect(sql).toContain('WITH')
      expect(sql).toContain('_cte')
      expect(sql).toContain('FULL JOIN')
    })

    test('应该为非CTE优化生成基础SQL', () => {
      const group: DataSourceGroup = {
        databaseId: 'db1',
        sqlModelId: 'realtime',
        infos: [
          { baseIndiceId: 'metric1', baseInfo: { code: 'metric1' } }
        ] as any,
        canUseCTE: false,
        groupType: 'realtime'
      }

      const context = {
        queryConfig: {
          fieldsBinding: {},
          filters: [],
          timeBucket: 'DAY'
        }
      }

      const sql = optimizer.optimizeQuery(group, context as any)
      
      expect(sql).toContain('Basic SQL')
      expect(sql).toContain('realtime')
    })
  })

  describe('分组统计', () => {
    test('应该计算正确的分组统计信息', () => {
      const originalInfos = [
        { baseIndiceId: 'metric1', databaseId: 'db1' },
        { baseIndiceId: 'metric2', databaseId: 'db1' },
        { baseIndiceId: 'metric3', databaseId: 'db2' }
      ]

      const groups: DataSourceGroup[] = [
        {
          databaseId: 'db1',
          infos: originalInfos.slice(0, 2) as any,
          canUseCTE: true,
          groupType: 'realtime'
        },
        {
          databaseId: 'db2',
          infos: originalInfos.slice(2) as any,
          canUseCTE: false,
          groupType: 'datawarehouse'
        }
      ]

      const stats = optimizer.getGroupingStatistics(originalInfos as any, groups)
      
      expect(stats.originalMetricCount).toBe(3)
      expect(stats.groupCount).toBe(2)
      expect(stats.optimizedGroupCount).toBe(1)
      expect(stats.optimizationRatio).toBe(0.5)
    })
  })

  describe('分组验证', () => {
    test('应该验证分组结果的正确性', () => {
      const originalInfos = [
        { baseIndiceId: 'metric1' },
        { baseIndiceId: 'metric2' },
        { baseIndiceId: 'metric3' }
      ]

      const validGroups: DataSourceGroup[] = [
        {
          databaseId: 'db1',
          infos: originalInfos.slice(0, 2) as any,
          canUseCTE: true,
          groupType: 'realtime'
        },
        {
          databaseId: 'db2',
          infos: originalInfos.slice(2) as any,
          canUseCTE: false,
          groupType: 'datawarehouse'
        }
      ]

      const isValid = optimizer.validateGrouping(originalInfos as any, validGroups)
      expect(isValid).toBe(true)
    })

    test('应该检测指标数量不匹配', () => {
      const originalInfos = [
        { baseIndiceId: 'metric1' },
        { baseIndiceId: 'metric2' },
        { baseIndiceId: 'metric3' }
      ]

      const invalidGroups: DataSourceGroup[] = [
        {
          databaseId: 'db1',
          infos: originalInfos.slice(0, 2) as any, // 缺少一个指标
          canUseCTE: true,
          groupType: 'realtime'
        }
      ]

      const isValid = optimizer.validateGrouping(originalInfos as any, invalidGroups)
      expect(isValid).toBe(false)
    })

    test('应该检测重复的指标', () => {
      const originalInfos = [
        { baseIndiceId: 'metric1' },
        { baseIndiceId: 'metric2' }
      ]

      const duplicateGroups: DataSourceGroup[] = [
        {
          databaseId: 'db1',
          infos: [
            { baseIndiceId: 'metric1' },
            { baseIndiceId: 'metric1' } // 重复
          ] as any,
          canUseCTE: true,
          groupType: 'realtime'
        }
      ]

      const isValid = optimizer.validateGrouping(originalInfos as any, duplicateGroups)
      expect(isValid).toBe(false)
    })
  })

  describe('性能估算', () => {
    test('应该估算性能提升', () => {
      const groups: DataSourceGroup[] = [
        {
          databaseId: 'db1',
          infos: [{ baseIndiceId: 'metric1' }, { baseIndiceId: 'metric2' }] as any,
          canUseCTE: true,
          groupType: 'realtime'
        },
        {
          databaseId: 'db2',
          infos: [{ baseIndiceId: 'metric3' }] as any,
          canUseCTE: false,
          groupType: 'datawarehouse'
        }
      ]

      const gain = optimizer.estimatePerformanceGain(groups)
      
      expect(gain).toBeGreaterThan(0)
      expect(gain).toBeLessThanOrEqual(0.5) // 最大50%提升
    })

    test('应该处理空分组的性能估算', () => {
      const groups: DataSourceGroup[] = []

      const gain = optimizer.estimatePerformanceGain(groups)
      expect(gain).toBe(0)
    })
  })
})
