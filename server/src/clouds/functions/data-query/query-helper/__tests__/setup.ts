/**
 * Jest测试设置文件
 */

// 全局测试设置
beforeAll(() => {
  // 设置测试环境
  process.env.NODE_ENV = 'test'
})

// 每个测试前的设置
beforeEach(() => {
  // 清除所有mock
  jest.clearAllMocks()
})

// 每个测试后的清理
afterEach(() => {
  // 清理测试数据
})

// 全局测试清理
afterAll(() => {
  // 清理资源
})

// Mock常用的外部依赖
jest.mock('@utils', () => ({
  tryJsonParse: jest.fn(),
  loopTree: jest.fn()
}))

jest.mock('@utils/util', () => ({
  flatMapAwaitAll: jest.fn(),
  mapAwaitAll: jest.fn(),
  recurMapFilters: jest.fn()
}))

// Mock外部查询函数
jest.mock('../../realtime-indices', () => ({
  queryRealtimeIndicesTable: jest.fn().mockResolvedValue([]),
  getCompositeFormulaWithCode: jest.fn(),
  resolveComplexColumn: jest.fn()
}))

jest.mock('../../sql-mapping-model', () => ({
  querySqlMappingModel: jest.fn().mockResolvedValue([])
}))

jest.mock('../../tindex', () => ({
  queryIndicatorData: jest.fn().mockResolvedValue([])
}))

jest.mock('../../raw-sql', () => ({
  queryRawSql: jest.fn().mockResolvedValue([])
}))

jest.mock('../../data-center', () => ({
  queryDataCenter: jest.fn().mockResolvedValue([])
}))

jest.mock('../../dataset', () => ({
  queryDataset: jest.fn().mockResolvedValue([])
}))

jest.mock('dayjs', () => {
  const originalDayjs = jest.requireActual('dayjs')
  return {
    __esModule: true,
    default: jest.fn(() => originalDayjs('2023-01-01'))
  }
})

jest.mock('lodash', () => {
  const originalLodash = jest.requireActual('lodash')
  return {
    __esModule: true,
    default: originalLodash
  }
})

// 全局测试工具函数
global.createMockQueryConfig = (overrides = {}) => ({
  type: 'indices',
  fieldsBinding: {
    dim1: { type: 'indicesDims', name: 'dim1' },
    metric1: { type: 'indicesSpec', name: 'metric1' }
  },
  filters: [],
  timeBucket: 'DAY',
  limit: 100,
  ...overrides
})

global.createMockContext = (overrides = {}) => ({
  queryConfig: global.createMockQueryConfig(),
  req: {
    masterRequester: {
      get: jest.fn(),
      post: jest.fn()
    }
  },
  db: {},
  infos: [],
  startTime: Date.now(),
  ...overrides
})

global.createMockQueryResult = (overrides = {}) => ({
  data: [{ metric1: 100, dim1: 'test' }],
  metadata: {
    queryType: 'test',
    dataSource: 'test_db',
    duration: 50,
    fromCache: false
  },
  performance: {
    executionTime: 50,
    rowCount: 1,
    metricCount: 1,
    optimized: true
  },
  ...overrides
})
