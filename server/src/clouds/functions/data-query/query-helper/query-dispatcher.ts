/**
 * 指标查询重构 - 查询分发器
 * 
 * 负责：
 * 1. 查询类型识别和分发
 * 2. 查询处理器管理和注册
 * 3. 与现有 queryByType 逻辑的兼容性
 * 4. 统一的查询入口和协调
 */

import _ from 'lodash'
import { RequestMethod } from 'umi-request'
import { DataSourceQueryConfig } from '@/types/data-source'
import {
  QueryDispatcher as IQueryDispatcher,
  QueryHandler,
  QueryContext,
  QueryResult,
  QueryType,
  QueryStatus,
  QueryError
} from './query-interfaces'
import { CheckDeriveOtherIndiceInfoPatched, loadIndicesExtraInfo } from './data-loader'

// 导入现有的查询函数以保持兼容性
import { queryRawSql } from '../raw-sql'
import { queryDataCenter } from '../data-center'
import { queryDataset } from '../dataset'
import { queryIndicatorData } from '../tindex'
import { queryAnyIndices } from '../any-indices'

/**
 * 查询分发器实现
 * 
 * 设计原则：
 * 1. 完全兼容现有的 queryByType 逻辑
 * 2. 支持渐进式迁移，新旧逻辑可以并存
 * 3. 为新的模块化查询处理器提供统一入口
 */
export class QueryDispatcher implements IQueryDispatcher {
  private handlers: Map<string, QueryHandler> = new Map()
  private handlerPriorities: Map<string, number> = new Map()

  /**
   * 注册查询处理器
   */
  registerHandler(handler: QueryHandler): void {
    const queryType = handler.getQueryType()
    this.handlers.set(queryType, handler)

    // 设置优先级
    const priority = handler.getPriority?.() ?? 0
    this.handlerPriorities.set(queryType, priority)

    console.log(`[QueryDispatcher] 注册查询处理器: ${queryType}, 优先级: ${priority}`)
  }

  /**
   * 获取适合的查询处理器
   */
  getHandler(infos: CheckDeriveOtherIndiceInfoPatched[]): QueryHandler | null {
    // 按优先级排序，选择第一个可以处理的处理器
    const sortedHandlers = Array.from(this.handlers.entries())
      .sort(([typeA], [typeB]) => {
        const priorityA = this.handlerPriorities.get(typeA) ?? 0
        const priorityB = this.handlerPriorities.get(typeB) ?? 0
        return priorityB - priorityA // 降序排列
      })

    for (const [, handler] of sortedHandlers) {
      if (handler.canHandle(infos)) {
        return handler
      }
    }

    return null
  }

  /**
   * 分发查询请求 - 主要入口函数
   * 
   * 这个函数完全兼容现有的 queryByType 逻辑，确保重构不破坏现有功能
   */
  async dispatch(
    queryConfig: DataSourceQueryConfig,
    req: Record<string, RequestMethod>,
    db: any,
    opts?: {
      rowPermission?: any
      additional?: (key: string, value: any) => any
    }
  ): Promise<any[]> {
    const { rowPermission = {}, additional } = opts || {}
    const requester = req.masterRequester
    const sCache = queryConfig.staleAfter ?? 'PT4H'

    try {
      // ==================== 保持现有逻辑完全不变 ====================

      // 1. rawSql 类型查询
      if (queryConfig.type === 'rawSql') {
        return queryRawSql(queryConfig, requester, rowPermission, additional)
      }

      // 2. dataCenter 类型查询
      if (queryConfig.type === 'dataCenter') {
        // 保持现有的数据库类型检测逻辑
        if (!queryConfig.dbType) {
          const connsRes = await requester.get(`/app/database-manage-v1/connection/all?system=indices&sCache=${sCache}`)
          const conn = _.find(connsRes.data, c => c.id === queryConfig.connId)
          queryConfig.dbType = _.toLower(conn?.type || 'MYSQL') as any
        }

        const queryLast = _.some(queryConfig.fieldsBinding, c => /last|first/i.test(c.aggMode))
        if (!queryConfig.rawDbType && queryConfig.dbId && queryLast) {
          const conn = await this.getConnByDbId(requester, queryConfig.dbId)
          queryConfig.rawDbType = _.toLower(conn?.showType || 'MYSQL') as any
        }

        // 一键查询明细检测
        if (this.isQueryDetailsMode(queryConfig.fieldsBinding, queryConfig.queryMode)) {
          queryConfig.queryMode = 'select'
        }

        return queryDataCenter(queryConfig, requester, rowPermission, additional)
      }

      // 3. dataset 类型查询
      if (queryConfig.type === 'dataset') {
        // 保持现有的数据集类型检测逻辑
        if (!queryConfig.dbType) {
          const dataset = await db?.Dataset.findByPk(queryConfig.datasetId)
          queryConfig.dbType = dataset?.dbType
        }

        const queryLast = _.some(queryConfig.fieldsBinding, c => /last|first/i.test(c.aggMode))
        if (!queryConfig.rawDbType && queryConfig.dbId && queryLast) {
          const conn = await this.getConnByDbId(requester, queryConfig.dbId)
          queryConfig.rawDbType = _.toLower(conn?.showType || 'MYSQL') as any
        }

        // 一键查询明细检测
        if (this.isQueryDetailsMode(queryConfig.fieldsBinding, queryConfig.queryMode)) {
          queryConfig.queryMode = 'select'
        }

        return queryDataset(queryConfig, requester, rowPermission, additional)
      }

      // 4. 指标库查询 - 这里是重构的重点
      // 保持现有的 doris 引擎判断逻辑
      if (process.env.DEFAULT_QUERY_ENGINE === 'doris') {
        return queryIndicatorData(queryConfig, req.masterRequester)
      }

      // ==================== 新的模块化查询逻辑 ====================

      // 尝试使用新的模块化查询处理器
      if (this.handlers.size > 0) {
        try {
          return await this.dispatchToModularHandlers(queryConfig, req, db)
        } catch (error) {
          console.warn('[QueryDispatcher] 模块化查询失败，回退到原有逻辑:', error)
          // 回退到原有逻辑
          return queryAnyIndices(queryConfig, req, db)
        }
      }

      // 如果没有注册任何处理器，使用原有逻辑
      return queryAnyIndices(queryConfig, req, db)

    } catch (error) {
      console.error('[QueryDispatcher] 查询分发失败:', error)
      throw error
    }
  }

  /**
   * 使用模块化处理器进行查询分发
   */
  private async dispatchToModularHandlers(
    queryConfig: DataSourceQueryConfig,
    req: Record<string, RequestMethod>,
    db: any
  ): Promise<any[]> {
    // 0. 确保查询处理器已初始化
    await ensureHandlersInitialized()

    // 1. 加载指标元数据
    const infos = await loadIndicesExtraInfo(queryConfig, req.masterRequester)

    // 2. 创建查询上下文
    const context: QueryContext = {
      queryConfig,
      req,
      db,
      infos,
      startTime: Date.now()
    }

    // 3. 选择合适的查询处理器
    const handler = this.getHandler(infos)
    if (!handler) {
      throw new Error('没有找到合适的查询处理器')
    }

    console.log(`[QueryDispatcher] 使用查询处理器: ${handler.getQueryType()}`)

    // 4. 执行查询
    const results = await handler.execute(context)

    // 5. 合并结果（如果有多个结果）
    if (results.length === 1) {
      return results[0].data
    }

    // 使用查询合并器合并多个结果
    const { queryMerger } = await import('./query-merger')
    return queryMerger.mergeResults(results, queryConfig)
  }

  // ==================== 辅助方法（保持与原有逻辑一致） ====================

  /**
   * 根据数据库ID获取连接信息
   */
  private async getConnByDbId(requester: RequestMethod, dbId: number): Promise<any> {
    const sCache = 'PT4H'
    const connsRes = await requester.get(`/app/database-manage-v1/connection/all?system=indices&sCache=${sCache}`)
    const dbsRes = await requester.get(`/app/database-manage-v1/database/all?system=indices&sCache=${sCache}`)
    const db = _.find(dbsRes.data, d => d.id === dbId)
    return _.find(connsRes.data, c => c.id === db?.connectId)
  }

  /**
   * 检测是否为查询明细模式
   */
  private isQueryDetailsMode(fieldsBinding: any, queryMode: string): boolean {
    // 保持与原有逻辑完全一致
    const hasOnlyDimensions = _.every(fieldsBinding, (field) => {
      return !field || field.type === 'indicesDims' || field.type === 'field'
    })
    return hasOnlyDimensions && queryMode !== 'select'
  }

  /**
   * 获取已注册的处理器列表（用于调试）
   */
  getRegisteredHandlers(): string[] {
    return Array.from(this.handlers.keys())
  }

  /**
   * 清除所有处理器（用于测试）
   */
  clearHandlers(): void {
    this.handlers.clear()
    this.handlerPriorities.clear()
  }
}

// 导出单例实例
export const queryDispatcher = new QueryDispatcher()

// 初始化默认查询处理器
initializeQueryHandlers()

// 自动注册所有查询处理器
async function initializeQueryHandlers() {
  try {
    // 注册实时指标查询处理器
    const { realtimeQueryHandler } = await import('./query-types/realtime-query')
    queryDispatcher.registerHandler(realtimeQueryHandler)

    // 注册数仓指标查询处理器
    const { datawarehouseQueryHandler } = await import('./query-types/datawarehouse-query')
    queryDispatcher.registerHandler(datawarehouseQueryHandler)

    // 注册mindex指标查询处理器
    const { mindexQueryHandler } = await import('./query-types/mindex-query')
    queryDispatcher.registerHandler(mindexQueryHandler)

    console.log('[QueryDispatcher] 所有查询处理器注册完成')
  } catch (error) {
    console.error('[QueryDispatcher] 查询处理器注册失败:', error)
  }
}

// 初始化处理器（延迟加载）
let handlersInitialized = false
async function ensureHandlersInitialized() {
  if (!handlersInitialized) {
    await initializeQueryHandlers()
    handlersInitialized = true
  }
}
