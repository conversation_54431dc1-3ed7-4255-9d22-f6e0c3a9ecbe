/**
 * 指标查询重构 - 通用接口定义
 * 
 * 本文件定义了重构后查询架构的核心接口和类型，确保：
 * 1. 与现有类型系统完全兼容
 * 2. 支持所有类型的指标查询（实时、mindex、数仓、复合）
 * 3. 为模块化架构提供统一的类型基础
 */

import { RequestMethod } from 'umi-request'
import { DataSourceQueryConfig } from '@/types/data-source'
import { CheckDeriveOtherIndiceInfoPatched } from './data-loader'

// ==================== 核心查询接口 ====================

/**
 * 查询上下文 - 包含查询执行所需的所有信息
 */
export interface QueryContext {
  /** 查询配置参数 */
  queryConfig: DataSourceQueryConfig
  /** 请求器实例，用于发起HTTP请求 */
  req: Record<string, RequestMethod>
  /** 数据库连接实例 */
  db: any
  /** 指标元数据信息列表 */
  infos: CheckDeriveOtherIndiceInfoPatched[]
  /** 查询开始时间，用于性能监控 */
  startTime?: number
}

/**
 * 查询结果 - 标准化的查询返回格式
 */
export interface QueryResult {
  /** 查询返回的数据行 */
  data: any[]
  /** 查询元数据信息 */
  metadata?: {
    /** 查询类型 */
    queryType?: string
    /** 数据源信息 */
    dataSource?: string
    /** 查询耗时（毫秒） */
    duration?: number
    /** 查询的SQL语句（调试用） */
    sql?: string
    /** 是否使用了缓存 */
    fromCache?: boolean
    /** 复合指标类型 */
    compositeType?: string
    /** 是否为复合指标 */
    isComposite?: boolean
    /** 子指标数量 */
    subMetricCount?: number
    /** 复合指标公式 */
    formula?: string
    /** SQL模型ID */
    sqlModel?: string
    /** 指标ID */
    metricId?: string
    /** 错误信息 */
    error?: string
  }
  /** 性能指标 */
  performance?: PerformanceMetrics
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  /** 查询执行时间（毫秒） */
  executionTime: number
  /** 数据库查询时间（毫秒） */
  dbQueryTime?: number
  /** 数据合并时间（毫秒） */
  mergeTime?: number
  /** 返回行数 */
  rowCount: number
  /** 查询的指标数量 */
  metricCount: number
  /** 是否使用了优化（CTE、分组等） */
  optimized?: boolean
}

/**
 * 查询处理器接口 - 所有查询处理器必须实现的接口
 */
export interface QueryHandler {
  /**
   * 判断是否可以处理给定的指标信息
   * @param infos 指标元数据信息列表
   * @returns 是否可以处理
   */
  canHandle(infos: CheckDeriveOtherIndiceInfoPatched[]): boolean

  /**
   * 执行查询
   * @param context 查询上下文
   * @returns 查询结果列表
   */
  execute(context: QueryContext): Promise<QueryResult[]>

  /**
   * 获取查询类型标识
   * @returns 查询类型字符串
   */
  getQueryType(): string

  /**
   * 获取处理器优先级（可选）
   * 数值越大优先级越高，用于处理器选择
   */
  getPriority?(): number
}

// ==================== 复合指标相关接口 ====================

/**
 * 复合指标类型枚举
 */
export enum CompositeMetricType {
  /** 纯实时复合指标 - 所有子指标都是实时指标 */
  PURE_REALTIME = 'pure_realtime',
  /** 混合复合指标 - 包含不同类型的子指标 */
  MIXED = 'mixed',
  /** 数仓复合指标 - 所有子指标都来自数仓 */
  DATAWAREHOUSE = 'datawarehouse'
}

/**
 * 复合指标处理器接口
 */
export interface CompositeMetricProcessor {
  /**
   * 检测复合指标类型
   * @param info 复合指标信息
   * @returns 复合指标类型
   */
  detectCompositeType(info: CheckDeriveOtherIndiceInfoPatched): CompositeMetricType

  /**
   * 处理复合指标计算
   * @param info 复合指标信息
   * @param context 查询上下文
   * @returns 计算结果
   */
  processComposite(
    info: CheckDeriveOtherIndiceInfoPatched,
    context: QueryContext
  ): Promise<QueryResult[]>

  /**
   * 验证复合指标依赖关系
   * @param info 复合指标信息
   * @returns 是否有循环依赖
   */
  validateDependencies(info: CheckDeriveOtherIndiceInfoPatched): boolean
}

// ==================== 查询分组和优化相关接口 ====================

/**
 * 数据源分组信息
 */
export interface DataSourceGroup {
  /** 数据源ID */
  databaseId: string
  /** SQL模型ID（可选） */
  sqlModelId?: string
  /** 分组内的指标信息 */
  infos: CheckDeriveOtherIndiceInfoPatched[]
  /** 是否可以使用CTE优化 */
  canUseCTE: boolean
  /** 分组类型 */
  groupType: 'realtime' | 'mindex' | 'datawarehouse' | 'mixed'
}

/**
 * 查询优化器接口
 */
export interface QueryOptimizer {
  /**
   * 按数据源分组指标
   * @param infos 指标信息列表
   * @returns 分组结果
   */
  groupByDataSource(infos: CheckDeriveOtherIndiceInfoPatched[]): DataSourceGroup[]

  /**
   * 优化查询SQL
   * @param group 数据源分组
   * @param context 查询上下文
   * @returns 优化后的SQL
   */
  optimizeQuery(group: DataSourceGroup, context: QueryContext): string

  /**
   * 判断是否可以使用CTE优化
   * @param group 数据源分组
   * @returns 是否可以使用CTE
   */
  canUseCTEOptimization(group: DataSourceGroup): boolean
}

// ==================== 查询合并相关接口 ====================

/**
 * 查询合并器接口
 */
export interface QueryMerger {
  /**
   * 合并多个查询结果
   * @param results 查询结果列表
   * @param queryConfig 查询配置
   * @returns 合并后的结果
   */
  mergeResults(
    results: QueryResult[],
    queryConfig: DataSourceQueryConfig
  ): Promise<any[]>

  /**
   * 处理列名冲突
   * @param data 数据
   * @param conflictStrategy 冲突处理策略
   * @returns 处理后的数据
   */
  resolveColumnConflicts(
    data: any[],
    conflictStrategy?: 'rename' | 'merge' | 'override'
  ): any[]

  /**
   * 应用排序和分页
   * @param data 数据
   * @param queryConfig 查询配置
   * @returns 处理后的数据
   */
  applySortingAndPaging(
    data: any[],
    queryConfig: DataSourceQueryConfig
  ): any[]
}

// ==================== 查询分发器接口 ====================

/**
 * 查询分发器接口
 */
export interface QueryDispatcher {
  /**
   * 注册查询处理器
   * @param handler 查询处理器
   */
  registerHandler(handler: QueryHandler): void

  /**
   * 分发查询请求
   * @param queryConfig 查询配置
   * @param req 请求器
   * @param db 数据库连接
   * @returns 查询结果
   */
  dispatch(
    queryConfig: DataSourceQueryConfig,
    req: Record<string, RequestMethod>,
    db: any
  ): Promise<any[]>

  /**
   * 获取适合的查询处理器
   * @param infos 指标信息列表
   * @returns 查询处理器
   */
  getHandler(infos: CheckDeriveOtherIndiceInfoPatched[]): QueryHandler | null
}

// ==================== 工具类型 ====================

/**
 * 查询类型枚举
 */
export enum QueryType {
  REALTIME = 'realtime',
  MINDEX = 'mindex',
  DATAWAREHOUSE = 'datawarehouse',
  COMPOSITE = 'composite',
  LEGACY = 'legacy'
}

/**
 * 查询状态枚举
 */
export enum QueryStatus {
  PENDING = 'pending',
  EXECUTING = 'executing',
  SUCCESS = 'success',
  ERROR = 'error',
  TIMEOUT = 'timeout'
}

/**
 * 错误信息接口
 */
export interface QueryError {
  code: string
  message: string
  details?: any
  stack?: string
}

/**
 * 查询执行选项
 */
export interface QueryExecutionOptions {
  /** 超时时间（毫秒） */
  timeout?: number
  /** 是否启用缓存 */
  enableCache?: boolean
  /** 是否启用性能监控 */
  enableProfiling?: boolean
  /** 是否启用调试模式 */
  debug?: boolean
  /** 最大重试次数 */
  maxRetries?: number
}
