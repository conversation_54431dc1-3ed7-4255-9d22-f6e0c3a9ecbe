/**
 * 指标查询重构 - 查询合并器
 * 
 * 负责：
 * 1. 迁移现有的 Arquero 合并逻辑
 * 2. 处理列名冲突
 * 3. 应用排序和分页
 * 4. 保持所有现有的合并优化
 */

import _ from 'lodash'
import { from } from 'arquero'
import { DataSourceQueryConfig } from '@/types/data-source'
import { QueryMerger as IQueryMerger, QueryResult } from './query-interfaces'
import { isAggCol } from './sql-builder'

/**
 * 查询合并器实现
 * 
 * 完全保留现有的 Arquero 合并逻辑，确保：
 * 1. 功能完整性 - 所有合并功能保持不变
 * 2. 性能保持 - 保留所有优化策略
 * 3. 兼容性 - 与现有代码完全兼容
 */
export class QueryMerger implements IQueryMerger {

  /**
   * 合并多个查询结果
   * 
   * 这个方法完全保留了 any-indices.ts 中的合并逻辑
   */
  async mergeResults(
    results: QueryResult[],
    queryConfig: DataSourceQueryConfig
  ): Promise<any[]> {
    // 如果只有一个结果，直接返回
    if (results.length === 1) {
      return this.applySortingAndPaging(results[0].data, queryConfig)
    }

    // 提取数据集合
    const resultSets = results.map(result => result.data)

    // 获取连接键
    const joinKeys = _.filter(queryConfig.fieldsBinding, c => !isAggCol(c)).map(c => [c.name, c.name])

    // 使用 Arquero 进行 fullJoin 合并（完全保留原有逻辑）
    const resData = queryConfig.queryMode === 'select'
      ? _.flatten(resultSets) // select 模式直接合并
      : resultSets
        .reduce((acc: any, d) => {
          if (_.isEmpty(acc)) return from(d)
          try {
            return acc.join_full(from(d), joinKeys)
          } catch (e) {
            console.warn('[QueryMerger] Arquero join 失败，跳过该结果集:', e)
            return acc
          }
        }, null)
        ?.objects() || []

    // 处理列名冲突（完全保留原有逻辑）
    const mergedData = this.resolveColumnConflicts(resData)

    // 应用排序和分页
    return this.applySortingAndPaging(mergedData, queryConfig)
  }

  /**
   * 处理列名冲突
   * 
   * 完全保留 any-indices.ts 中的列名冲突处理逻辑
   */
  resolveColumnConflicts(
    data: any[],
    conflictStrategy: 'rename' | 'merge' | 'override' = 'merge'
  ): any[] {
    return data.map(obj => {
      const newObj = { ...obj }

      // 动态检测所有可能的列名后缀（如 _1, _2, _3 等）
      const fieldKeys = Object.keys(obj).filter(key => /_\d+$/.test(key))
      const baseKeys = _.uniq(fieldKeys.map(key => key.replace(/_\d+$/, '')))

      // 遍历所有需要处理的连接键字段
      baseKeys.forEach(field => {
        // 动态检测所有可能的列名后缀（如 _1, _2, _3 等）
        const keys = Object.keys(obj).filter(key => key.startsWith(`${field}_`))

        if (conflictStrategy === 'merge') {
          // 合并所有后缀列的值（优先取靠前的表）
          newObj[field] = keys.reduce((acc, key) => acc ?? obj[key], null) ?? obj[field]
        } else if (conflictStrategy === 'rename') {
          // 保留所有列，不做合并
          return newObj
        } else if (conflictStrategy === 'override') {
          // 使用最后一个值覆盖
          const lastKey = keys[keys.length - 1]
          if (lastKey) {
            newObj[field] = obj[lastKey] ?? obj[field]
          }
        }

        // 删除临时列
        keys.forEach(key => delete newObj[key])
      })

      return newObj
    })
  }

  /**
   * 应用排序和分页
   * 
   * 完全保留 any-indices.ts 中的排序和分页逻辑
   */
  applySortingAndPaging(
    data: any[],
    queryConfig: DataSourceQueryConfig
  ): any[] {
    let processedData = data

    // 1. 排序处理（完全保留原有逻辑）
    if (!_.isEmpty(queryConfig.orderBy)) {
      processedData = _.orderBy(
        processedData,
        queryConfig.orderBy.map(o => {
          const { field, col } = o
          return col || queryConfig.fieldsBinding?.[field]?.name || field
        }),
        queryConfig.orderBy.map(o => o.dir)
      )
    }

    // 2. 分页处理
    if (queryConfig.limit || queryConfig.page || queryConfig.offset) {
      const limit = queryConfig.limit || 1000
      const offset = queryConfig.offset || ((queryConfig.page || 1) - 1) * limit

      processedData = processedData.slice(offset, offset + limit)
    }

    return processedData
  }

  /**
   * 合并查询总计结果
   * 
   * 处理 queryTotal 模式的特殊合并逻辑
   */
  async mergeQueryTotalResults(
    results: QueryResult[],
    queryConfig: DataSourceQueryConfig
  ): Promise<any[]> {
    if (!queryConfig.queryTotal) {
      return this.mergeResults(results, queryConfig)
    }

    // 提取结果集和总计信息
    const validColls = results.map(result => ({
      ...result.metadata,
      resultSet: result.data
    }))

    const resultSets = validColls.map(coll => coll.resultSet)

    // 获取连接键
    const joinKeys = _.filter(queryConfig.fieldsBinding, c => !isAggCol(c)).map(c => [c.name, c.name])

    // 合并结果集
    const mergedResultSet = queryConfig.queryMode === 'select'
      ? _.flatten(resultSets)
      : resultSets
        .reduce((acc: any, d) => {
          if (_.isEmpty(acc)) return from(d)
          try {
            return acc.join_full(from(d), joinKeys)
          } catch (e) {
            console.warn('[QueryMerger] QueryTotal Arquero join 失败:', e)
            return acc
          }
        }, null)
        ?.objects() || []

    // 处理列名冲突
    const processedResultSet = this.resolveColumnConflicts(mergedResultSet)

    // 应用排序
    const sorted = this.applySortingAndPaging(processedResultSet, queryConfig)

    // 构造最终结果（保持与原有格式一致）
    return [_.assign({}, ...validColls.map(coll => _.omit(coll, 'resultSet')), { resultSet: sorted })]
  }

  /**
   * 合并不同类型的查询结果
   * 
   * 用于处理混合查询（实时 + 数仓 + mindex）的结果合并
   */
  mergeHeterogeneousResults(
    realtimeResults: QueryResult[],
    datawarehouseResults: QueryResult[],
    mindexResults: QueryResult[],
    queryConfig: DataSourceQueryConfig
  ): Promise<any[]> {
    // 合并所有结果
    const allResults = [
      ...realtimeResults,
      ...datawarehouseResults,
      ...mindexResults
    ]

    return this.mergeResults(allResults, queryConfig)
  }

  /**
   * 验证合并结果的完整性
   */
  validateMergedResults(
    originalResults: QueryResult[],
    mergedData: any[],
    queryConfig: DataSourceQueryConfig
  ): boolean {
    try {
      // 1. 检查数据行数是否合理
      const totalOriginalRows = originalResults.reduce((sum, result) => sum + result.data.length, 0)

      if (queryConfig.queryMode === 'select') {
        // select 模式应该是所有行的总和
        return mergedData.length === totalOriginalRows
      } else {
        // groupBy 模式的行数可能因为 join 而变化，只检查不为空
        return mergedData.length > 0 || totalOriginalRows === 0
      }
    } catch (error) {
      console.warn('[QueryMerger] 合并结果验证失败:', error)
      return false
    }
  }

  /**
   * 获取合并统计信息
   */
  getMergeStatistics(
    originalResults: QueryResult[],
    mergedData: any[]
  ): {
    originalResultCount: number
    originalRowCount: number
    mergedRowCount: number
    mergeRatio: number
  } {
    const originalRowCount = originalResults.reduce((sum, result) => sum + result.data.length, 0)

    return {
      originalResultCount: originalResults.length,
      originalRowCount,
      mergedRowCount: mergedData.length,
      mergeRatio: originalRowCount > 0 ? mergedData.length / originalRowCount : 0
    }
  }

  /**
   * 计算合并统计信息
   */
  calculateMergeStatistics(results: QueryResult[]): {
    totalExecutionTime: number
    totalRowCount: number
    totalMetricCount: number
    optimizedQueries: number
    queryTypes: string[]
  } {
    const totalExecutionTime = results.reduce((sum, r) =>
      sum + (r.performance?.executionTime || 0), 0
    )

    const totalRowCount = results.reduce((sum, r) =>
      sum + (r.performance?.rowCount || 0), 0
    )

    const totalMetricCount = results.reduce((sum, r) =>
      sum + (r.performance?.metricCount || 0), 0
    )

    const optimizedQueries = results.filter(r =>
      r.performance?.optimized
    ).length

    const queryTypes = _.uniq(results.map(r =>
      r.metadata?.queryType
    ).filter(Boolean))

    return {
      totalExecutionTime,
      totalRowCount,
      totalMetricCount,
      optimizedQueries,
      queryTypes
    }
  }
}

// 导出单例实例
export const queryMerger = new QueryMerger()
