/**
 * 指标查询重构 - 查询优化器
 * 
 * 负责：
 * 1. 数据源分组优化
 * 2. CTE查询优化
 * 3. 智能查询策略
 * 4. 性能监控和统计
 */

import _ from 'lodash'
import {
  QueryOptimizer as IQueryOptimizer,
  DataSourceGroup,
  QueryContext
} from './query-interfaces'
import { CheckDeriveOtherIndiceInfoPatched } from './data-loader'
import { isAggCol } from './sql-builder'

/**
 * 查询优化器实现
 * 
 * 设计原则：
 * 1. 按数据源ID智能分组
 * 2. 同源指标合并查询以提升性能
 * 3. 支持CTE优化策略
 * 4. 保持查询结果的正确性
 */
export class QueryOptimizer implements IQueryOptimizer {

  /**
   * 按数据源分组指标
   * 
   * 这是核心优化策略：将相同数据源的指标分组，以便合并查询
   */
  groupByDataSource(infos: CheckDeriveOtherIndiceInfoPatched[]): DataSourceGroup[] {
    console.log('[QueryOptimizer] 开始按数据源分组指标')

    // 1. 按数据源ID和SQL模型ID进行分组
    const groupKey = (info: CheckDeriveOtherIndiceInfoPatched) => {
      const databaseId = info.databaseId || 'default'
      const sqlModelId = info.sqlModelId || 'default'
      return `${databaseId}:${sqlModelId}`
    }

    const groups = _.groupBy(infos, groupKey)

    // 2. 构造分组结果
    const result = Object.entries(groups).map(([key, groupInfos]) => {
      const [databaseId, sqlModelId] = key.split(':')

      const group: DataSourceGroup = {
        databaseId,
        sqlModelId: sqlModelId === 'default' ? undefined : sqlModelId,
        infos: groupInfos,
        canUseCTE: this.canUseCTEOptimization({
          databaseId,
          sqlModelId: sqlModelId === 'default' ? undefined : sqlModelId,
          infos: groupInfos,
          canUseCTE: false, // 临时值，下面会重新计算
          groupType: this.determineGroupType(groupInfos)
        }),
        groupType: this.determineGroupType(groupInfos)
      }

      // 重新设置canUseCTE
      group.canUseCTE = this.canUseCTEOptimization(group)

      return group
    })

    console.log(`[QueryOptimizer] 分组完成，共${result.length}个组:`,
      result.map(g => `${g.groupType}(${g.infos.length}个指标)`).join(', '))

    return result
  }

  /**
   * 优化查询SQL
   */
  optimizeQuery(group: DataSourceGroup, context: QueryContext): string {
    const { queryConfig } = context

    if (group.groupType === 'realtime' && group.canUseCTE && group.infos.length > 1) {
      return this.buildCTEOptimizedSQL(group, context)
    }

    if (group.groupType === 'datawarehouse') {
      return this.buildDatawarehouseOptimizedSQL(group, context)
    }

    // 默认情况，返回基础SQL
    return this.buildBasicSQL(group, context)
  }

  /**
   * 判断是否可以使用CTE优化
   */
  canUseCTEOptimization(group: DataSourceGroup): boolean {
    // 1. 必须是同一个数据源
    if (!group.databaseId || group.databaseId === 'default') {
      return false
    }

    // 2. 必须有多个指标
    if (group.infos.length <= 1) {
      return false
    }

    // 3. 实时指标最适合CTE优化
    if (group.groupType === 'realtime') {
      return true
    }

    // 4. 数仓指标如果是同一个SQL模型，也可以使用CTE
    if (group.groupType === 'datawarehouse' && group.sqlModelId) {
      return group.infos.every(info => info.sqlModelId === group.sqlModelId)
    }

    return false
  }

  // ==================== 私有方法 ====================

  /**
   * 确定分组类型
   */
  private determineGroupType(infos: CheckDeriveOtherIndiceInfoPatched[]): DataSourceGroup['groupType'] {
    const types = infos.map(info => {
      if (info.sqlModelId === 'realtime' || (!info.sqlModelId && info.baseInfo && !info.baseInfo.isLanding)) {
        return 'realtime'
      }
      if (info.sqlModelId === 'mindex' || (!info.sqlModelId && info.baseInfo && info.baseInfo.isLanding)) {
        return 'mindex'
      }
      if (info.sqlModelId && info.sqlModelId !== 'realtime' && info.sqlModelId !== 'mindex') {
        return 'datawarehouse'
      }
      return 'realtime' // 默认
    })

    const uniqueTypes = _.uniq(types)

    if (uniqueTypes.length === 1) {
      return uniqueTypes[0] as DataSourceGroup['groupType']
    }

    return 'mixed'
  }

  /**
   * 构建CTE优化的SQL
   */
  private buildCTEOptimizedSQL(group: DataSourceGroup, context: QueryContext): string {
    const { queryConfig } = context
    const { fieldsBinding, filters, timeBucket, orderBy, limit } = queryConfig

    // 获取维度和指标
    const [dims] = _.partition(fieldsBinding, c => !isAggCol(c))

    // 构建CTE子查询
    const cteQueries = group.infos.map((info, index) => {
      const metricName = info.baseInfo?.code || `metric_${index}`
      return `
        ${metricName}_cte AS (
          SELECT ${dims.map(d => d.name).join(', ')}, 
                 ${metricName}
          FROM ${this.getTableName(info)}
          WHERE ${this.buildWhereClause(filters, timeBucket)}
        )`
    })

    // 构建主查询
    const mainQuery = `
      SELECT ${dims.map(d => d.name).join(', ')},
             ${group.infos.map(info => info.baseInfo?.code).join(', ')}
      FROM ${group.infos[0].baseInfo?.code}_cte
      ${group.infos.slice(1).map(info =>
      `FULL JOIN ${info.baseInfo?.code}_cte USING (${dims.map(d => d.name).join(', ')})`
    ).join(' ')}
      ${orderBy?.length ? `ORDER BY ${this.buildOrderByClause(orderBy)}` : ''}
      ${limit ? `LIMIT ${limit}` : ''}
    `

    return `WITH ${cteQueries.join(', ')} ${mainQuery}`
  }

  /**
   * 构建数仓优化的SQL
   */
  private buildDatawarehouseOptimizedSQL(group: DataSourceGroup, context: QueryContext): string {
    // 数仓指标的SQL优化逻辑
    // 这里可以实现特定的数仓查询优化
    return this.buildBasicSQL(group, context)
  }

  /**
   * 构建基础SQL
   */
  private buildBasicSQL(group: DataSourceGroup, _context: QueryContext): string {
    // 基础SQL构建逻辑
    return `-- Basic SQL for ${group.groupType} group with ${group.infos.length} metrics`
  }

  /**
   * 获取表名
   */
  private getTableName(info: CheckDeriveOtherIndiceInfoPatched): string {
    // baseInfo中没有tableName属性，使用其他方式构造表名
    return info.baseInfo?.code || `table_${info.baseIndiceId}`
  }

  /**
   * 构建WHERE子句
   */
  private buildWhereClause(filters: any[], _timeBucket: string): string {
    // 构建过滤条件
    const conditions = ['1=1'] // 基础条件

    if (filters?.length) {
      // 添加过滤条件
      conditions.push(...filters.map(f => this.buildFilterCondition(f)))
    }

    return conditions.join(' AND ')
  }

  /**
   * 构建过滤条件
   */
  private buildFilterCondition(filter: any): string {
    // 根据过滤器类型构建条件
    return `${filter.col} ${filter.op} ${filter.eq}`
  }

  /**
   * 构建ORDER BY子句
   */
  private buildOrderByClause(orderBy: any[]): string {
    return orderBy.map(o => `${o.field} ${o.dir}`).join(', ')
  }

  /**
   * 获取分组统计信息
   */
  getGroupingStatistics(
    originalInfos: CheckDeriveOtherIndiceInfoPatched[],
    groups: DataSourceGroup[]
  ): {
    originalMetricCount: number
    groupCount: number
    optimizedGroupCount: number
    optimizationRatio: number
  } {
    const optimizedGroupCount = groups.filter(g => g.canUseCTE).length

    return {
      originalMetricCount: originalInfos.length,
      groupCount: groups.length,
      optimizedGroupCount,
      optimizationRatio: groups.length > 0 ? optimizedGroupCount / groups.length : 0
    }
  }

  /**
   * 验证分组结果
   */
  validateGrouping(
    originalInfos: CheckDeriveOtherIndiceInfoPatched[],
    groups: DataSourceGroup[]
  ): boolean {
    // 验证所有指标都被分组
    const groupedInfoCount = groups.reduce((sum, group) => sum + group.infos.length, 0)

    if (groupedInfoCount !== originalInfos.length) {
      console.warn('[QueryOptimizer] 分组验证失败：指标数量不匹配')
      return false
    }

    // 验证没有重复的指标
    const allGroupedIds = groups.flatMap(g => g.infos.map(info => info.baseIndiceId))
    const uniqueIds = _.uniq(allGroupedIds)

    if (uniqueIds.length !== allGroupedIds.length) {
      console.warn('[QueryOptimizer] 分组验证失败：存在重复指标')
      return false
    }

    return true
  }

  /**
   * 估算查询性能提升
   */
  estimatePerformanceGain(groups: DataSourceGroup[]): number {
    // 估算性能提升百分比
    const totalMetrics = groups.reduce((sum, g) => sum + g.infos.length, 0)
    const optimizedMetrics = groups
      .filter(g => g.canUseCTE)
      .reduce((sum, g) => sum + g.infos.length, 0)

    if (totalMetrics === 0) return 0

    // 假设CTE优化可以带来50%的性能提升
    return (optimizedMetrics / totalMetrics) * 0.5
  }
}

// 导出单例实例
export const queryOptimizer = new QueryOptimizer()
