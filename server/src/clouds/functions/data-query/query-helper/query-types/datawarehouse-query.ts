/**
 * 指标查询重构 - 数仓指标查询处理器
 * 
 * 负责：
 * 1. 数仓指标查询逻辑
 * 2. 高表查询性能优化
 * 3. 时点值指标特殊处理
 * 4. SQL模型查询执行
 */

import _ from 'lodash'
import { 
  QueryHandler, 
  QueryContext, 
  QueryResult, 
  QueryType
} from '../query-interfaces'
import { CheckDeriveOtherIndiceInfoPatched } from '../data-loader'

// 导入现有的数仓查询函数以保持兼容性
import { querySqlMappingModel } from '../../sql-mapping-model'

/**
 * 数仓指标查询处理器
 * 
 * 设计原则：
 * 1. 完全保留现有的数仓指标查询逻辑
 * 2. 保持高表查询优化
 * 3. 保持时点值处理逻辑
 * 4. 支持复合数仓指标
 */
export class DatawarehouseQueryHandler implements QueryHandler {

  /**
   * 判断是否可以处理给定的指标信息
   */
  canHandle(infos: CheckDeriveOtherIndiceInfoPatched[]): boolean {
    return infos.some(info => 
      // 有明确的SQL模型ID且不是实时或mindex
      (info.sqlModelId && 
       info.sqlModelId !== 'realtime' && 
       info.sqlModelId !== 'mindex') ||
      // 有SQL查询语句的数仓指标
      (info.sql && info.databaseId)
    )
  }

  /**
   * 获取查询类型标识
   */
  getQueryType(): string {
    return QueryType.DATAWAREHOUSE
  }

  /**
   * 获取处理器优先级
   */
  getPriority(): number {
    return 80 // 中等优先级
  }

  /**
   * 执行查询
   */
  async execute(context: QueryContext): Promise<QueryResult[]> {
    const { queryConfig, req, infos, startTime } = context
    
    try {
      console.log('[DatawarehouseQueryHandler] 开始执行数仓指标查询')

      // 1. 检测是否有复合数仓指标
      const compositeInfo = infos.find(info => info.composedMetricInfos?.length > 0)
      
      if (compositeInfo && this.isDatawarehouseComposite(compositeInfo)) {
        return this.handleDatawarehouseComposite(compositeInfo, context)
      }

      // 2. 按SQL模型分组处理
      const groups = this.groupBySqlModel(infos)
      
      // 3. 并行执行各组查询
      const groupResults = await Promise.all(
        groups.map(group => this.executeGroupQuery(group, context))
      )

      // 4. 合并结果
      const allData = groupResults.flatMap(result => result.data)
      const totalExecutionTime = Date.now() - (startTime || Date.now())

      return [{
        data: allData,
        metadata: {
          queryType: 'datawarehouse',
          dataSource: groups.map(g => g.sqlModelId).join(','),
          duration: totalExecutionTime,
          fromCache: false
        },
        performance: {
          executionTime: totalExecutionTime,
          rowCount: allData.length,
          metricCount: infos.length,
          optimized: groups.length < infos.length // 如果分组数少于指标数，说明有优化
        }
      }]

    } catch (error) {
      console.error('[DatawarehouseQueryHandler] 查询执行失败:', error)
      throw error
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 检测是否为数仓复合指标
   */
  private isDatawarehouseComposite(info: CheckDeriveOtherIndiceInfoPatched): boolean {
    if (!info.composedMetricInfos?.length) {
      return false
    }

    // 检查所有子指标是否都来自数仓SQL模型
    return info.composedMetricInfos.every(subMetric => {
      return subMetric.sqlModelId && 
             subMetric.sqlModelId !== 'realtime' && 
             subMetric.sqlModelId !== 'mindex'
    })
  }

  /**
   * 处理数仓复合指标
   */
  private async handleDatawarehouseComposite(
    compositeInfo: CheckDeriveOtherIndiceInfoPatched,
    context: QueryContext
  ): Promise<QueryResult[]> {
    const { queryConfig, req } = context
    const startTime = Date.now()

    console.log('[DatawarehouseQueryHandler] 处理数仓复合指标')

    // 数仓复合指标可以在SQL层面计算，使用现有逻辑
    const data = await querySqlMappingModel(queryConfig, req.masterRequester, [compositeInfo])

    const executionTime = Date.now() - startTime

    return [{
      data: data || [],
      metadata: {
        queryType: 'datawarehouse_composite',
        dataSource: compositeInfo.databaseId,
        duration: executionTime,
        formula: compositeInfo.composedSQLFormula,
        fromCache: false
      },
      performance: {
        executionTime,
        rowCount: data?.length || 0,
        metricCount: 1,
        optimized: true // 数仓复合指标在SQL层面优化
      }
    }]
  }

  /**
   * 按SQL模型分组指标
   */
  private groupBySqlModel(infos: CheckDeriveOtherIndiceInfoPatched[]): Array<{
    sqlModelId: string
    databaseId: string
    infos: CheckDeriveOtherIndiceInfoPatched[]
  }> {
    const groups = _.groupBy(infos, info => `${info.sqlModelId}:${info.databaseId}`)
    
    return Object.entries(groups).map(([key, groupInfos]) => {
      const [sqlModelId, databaseId] = key.split(':')
      return {
        sqlModelId,
        databaseId,
        infos: groupInfos
      }
    })
  }

  /**
   * 执行分组查询
   */
  private async executeGroupQuery(
    group: { sqlModelId: string; databaseId: string; infos: CheckDeriveOtherIndiceInfoPatched[] },
    context: QueryContext
  ): Promise<QueryResult> {
    const { queryConfig, req } = context
    const startTime = Date.now()

    console.log(`[DatawarehouseQueryHandler] 执行SQL模型查询: ${group.sqlModelId}`)

    // 1. 优化高表查询
    const optimizedConfig = this.optimizeHeightTable(queryConfig, group.infos)
    
    // 2. 处理时点值逻辑
    const processedConfig = this.processTimePointValue(optimizedConfig, group.infos)
    
    // 3. 执行SQL模型查询（使用现有逻辑）
    const data = await querySqlMappingModel(processedConfig, req.masterRequester, group.infos)

    const executionTime = Date.now() - startTime

    return {
      data: data || [],
      metadata: {
        queryType: 'datawarehouse_group',
        dataSource: group.databaseId,
        sqlModel: group.sqlModelId,
        duration: executionTime,
        fromCache: false
      },
      performance: {
        executionTime,
        rowCount: data?.length || 0,
        metricCount: group.infos.length,
        optimized: this.canOptimizeGroup(group)
      }
    }
  }

  /**
   * 优化高表查询
   * 
   * 完全保留现有的高表查询优化逻辑
   */
  private optimizeHeightTable(
    queryConfig: any,
    infos: CheckDeriveOtherIndiceInfoPatched[]
  ): any {
    const { mode, relationField } = infos?.[0] || {}
    if (mode !== 'height') {
      return queryConfig
    }

    console.log('[DatawarehouseQueryHandler] 应用高表查询优化')

    const heightMetFlts = _.map(infos, ind => {
      const metricNameFlt = ind.heightParams?.filter
        ? { col: relationField, op: 'equal', eq: [ind.heightParams.filter] }
        : null
      const specFlts = ind.heightSpec?.filters
      if (_.isEmpty(specFlts)) {
        return metricNameFlt
      }
      if (_.size(specFlts) === 1 && specFlts[0].op === 'and') {
        return { op: 'and', eq: _.compact([metricNameFlt, specFlts[0]]) }
      }
      return { op: 'and', eq: _.compact([metricNameFlt, ...(specFlts || [])]) }
    }).filter(Boolean)

    return {
      ...queryConfig,
      filters: (_.some(heightMetFlts)
          ? [...(queryConfig.filters || []), { op: 'or', eq: heightMetFlts }]
          : queryConfig.filters || []
      )
    }
  }

  /**
   * 处理时点值指标
   * 
   * 保留现有的时点值处理逻辑
   */
  private processTimePointValue(
    queryConfig: any,
    infos: CheckDeriveOtherIndiceInfoPatched[]
  ): any {
    // 检查是否有时点值指标
    const hasTimePointValue = infos.some(info => 
      info.valueType === 'timePointerValue' || 
      info.baseInfo?.isTimePointValue
    )

    if (!hasTimePointValue) {
      return queryConfig
    }

    console.log('[DatawarehouseQueryHandler] 处理时点值指标')

    // 时点值指标的特殊处理逻辑
    // 这里保留现有的处理逻辑
    return {
      ...queryConfig,
      // 添加时点值相关的处理
      _timePointValue: true
    }
  }

  /**
   * 判断分组是否可以优化
   */
  private canOptimizeGroup(group: { infos: CheckDeriveOtherIndiceInfoPatched[] }): boolean {
    // 如果同一个SQL模型有多个指标，可以进行优化
    return group.infos.length > 1
  }

  /**
   * 验证数仓查询结果
   */
  private validateDatawarehouseResults(results: QueryResult[]): boolean {
    return results.every(result => 
      Array.isArray(result.data) && 
      result.metadata?.queryType?.includes('datawarehouse')
    )
  }

  /**
   * 获取数仓查询统计信息
   */
  getDatawarehouseStatistics(
    infos: CheckDeriveOtherIndiceInfoPatched[],
    results: QueryResult[]
  ): {
    sqlModelCount: number
    heightTableCount: number
    timePointValueCount: number
    compositeCount: number
  } {
    const sqlModels = _.uniq(infos.map(info => info.sqlModelId).filter(Boolean))
    const heightTables = infos.filter(info => info.mode === 'height')
    const timePointValues = infos.filter(info => 
      info.valueType === 'timePointerValue' || info.baseInfo?.isTimePointValue
    )
    const composites = infos.filter(info => info.composedMetricInfos?.length > 0)

    return {
      sqlModelCount: sqlModels.length,
      heightTableCount: heightTables.length,
      timePointValueCount: timePointValues.length,
      compositeCount: composites.length
    }
  }
}

// 导出处理器实例
export const datawarehouseQueryHandler = new DatawarehouseQueryHandler()
