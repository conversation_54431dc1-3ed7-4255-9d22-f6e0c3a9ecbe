/**
 * 指标查询重构 - mindex指标查询处理器
 * 
 * 负责：
 * 1. 传统指标库查询
 * 2. 批量查询优化
 * 3. 查询失败降级策略
 * 4. 性能监控和告警
 */

import _ from 'lodash'
import {
  QueryHandler,
  QueryContext,
  QueryResult,
  QueryType
} from '../query-interfaces'
import { CheckDeriveOtherIndiceInfoPatched } from '../data-loader'

// 导入现有的mindex查询函数以保持兼容性
import { queryIndicatorData } from '../../tindex'

/**
 * mindex指标查询处理器
 * 
 * 设计原则：
 * 1. 完全保留现有的mindex指标查询逻辑
 * 2. 实现批量查询优化
 * 3. 保持查询失败降级策略
 * 4. 提供性能监控和告警
 */
export class MindexQueryHandler implements QueryHandler {

  /**
   * 判断是否可以处理给定的指标信息
   */
  canHandle(infos: CheckDeriveOtherIndiceInfoPatched[]): boolean {
    return infos.some(info =>
      // 明确标识为mindex的指标
      info.sqlModelId === 'mindex' ||
      // 没有sqlModelId但有baseInfo且isLanding为true的传统指标
      (!info.sqlModelId && info.baseInfo && info.baseInfo.isLanding) ||
      // 有tableId的传统指标查询
      (!info.sqlModelId && !info.sql && !info.databaseId)
    )
  }

  /**
   * 获取查询类型标识
   */
  getQueryType(): string {
    return QueryType.MINDEX
  }

  /**
   * 获取处理器优先级
   */
  getPriority(): number {
    return 60 // 较低优先级，作为兜底方案
  }

  /**
   * 执行查询
   */
  async execute(context: QueryContext): Promise<QueryResult[]> {
    const { queryConfig, req, infos } = context

    try {
      console.log('[MindexQueryHandler] 开始执行mindex指标查询')

      // 1. 尝试批量查询
      const batchResult = await this.tryBatchQuery(context)

      if (batchResult) {
        return [batchResult]
      }

      // 2. 批量查询失败，降级到单个查询
      console.warn('[MindexQueryHandler] 批量查询失败，降级到单个查询')
      return this.fallbackToSingleQueries(context)

    } catch (error) {
      console.error('[MindexQueryHandler] 查询执行失败:', error)

      // 3. 如果还是失败，尝试最后的降级策略
      return this.handleQueryFailure(error, context)
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 尝试批量查询
   */
  private async tryBatchQuery(context: QueryContext): Promise<QueryResult | null> {
    const { queryConfig, req } = context
    const startTime = Date.now()

    try {
      console.log('[MindexQueryHandler] 尝试批量查询')

      // 使用现有的 queryIndicatorData 逻辑
      const data = await queryIndicatorData(queryConfig, req.masterRequester)

      const executionTime = Date.now() - startTime

      return {
        data: data || [],
        metadata: {
          queryType: 'mindex_batch',
          dataSource: String(queryConfig.tableId || 'unknown'),
          duration: executionTime,
          fromCache: false
        },
        performance: {
          executionTime,
          rowCount: data?.length || 0,
          metricCount: context.infos.length,
          optimized: true // 批量查询是优化的
        }
      }

    } catch (error) {
      console.warn('[MindexQueryHandler] 批量查询失败:', error)
      return null
    }
  }

  /**
   * 降级到单个查询
   */
  private async fallbackToSingleQueries(context: QueryContext): Promise<QueryResult[]> {
    const { queryConfig, infos } = context

    console.log('[MindexQueryHandler] 执行单个查询降级策略')

    const results: QueryResult[] = []

    // 为每个指标单独执行查询
    for (const info of infos) {
      try {
        const singleResult = await this.executeSingleQuery(info, context)
        results.push(singleResult)
      } catch (error) {
        console.error(`[MindexQueryHandler] 单个指标查询失败: ${info.baseIndiceId}`, error)

        // 为失败的指标创建空结果
        results.push({
          data: [],
          metadata: {
            queryType: 'mindex_single_failed',
            dataSource: String(queryConfig.tableId || 'unknown'),
            duration: 0,
            error: error.message,
            fromCache: false
          },
          performance: {
            executionTime: 0,
            rowCount: 0,
            metricCount: 1,
            optimized: false
          }
        })
      }
    }

    return results
  }

  /**
   * 执行单个指标查询
   */
  private async executeSingleQuery(
    info: CheckDeriveOtherIndiceInfoPatched,
    context: QueryContext
  ): Promise<QueryResult> {
    const { queryConfig, req } = context
    const startTime = Date.now()

    // 构造单个指标的查询配置
    const singleQueryConfig = {
      ...queryConfig,
      fieldsBinding: this.buildSingleMetricFieldsBinding(info, queryConfig.fieldsBinding)
    }

    const data = await queryIndicatorData(singleQueryConfig, req.masterRequester)

    const executionTime = Date.now() - startTime

    return {
      data: data || [],
      metadata: {
        queryType: 'mindex_single',
        dataSource: String(queryConfig.tableId || 'unknown'),
        metricId: info.baseIndiceId,
        duration: executionTime,
        fromCache: false
      },
      performance: {
        executionTime,
        rowCount: data?.length || 0,
        metricCount: 1,
        optimized: false
      }
    }
  }

  /**
   * 构造单个指标的字段绑定
   */
  private buildSingleMetricFieldsBinding(
    info: CheckDeriveOtherIndiceInfoPatched,
    originalFieldsBinding: any
  ): any {
    // 只保留当前指标相关的字段绑定
    const metricFields = _.pickBy(originalFieldsBinding, (field, key) => {
      return field.type !== 'indicesSpec' || field.id === info.baseIndiceId
    })

    return metricFields
  }

  /**
   * 处理查询失败
   */
  private async handleQueryFailure(
    error: any,
    context: QueryContext
  ): Promise<QueryResult[]> {
    const { queryConfig, infos } = context

    console.error('[MindexQueryHandler] 所有查询策略都失败，返回错误结果')

    // 返回包含错误信息的结果
    return [{
      data: [],
      metadata: {
        queryType: 'mindex_failed',
        dataSource: String(queryConfig.tableId || 'unknown'),
        duration: 0,
        error: error.message || '查询失败',
        fromCache: false
      },
      performance: {
        executionTime: 0,
        rowCount: 0,
        metricCount: infos.length,
        optimized: false
      }
    }]
  }

  /**
   * 验证mindex查询结果
   */
  private validateMindexResults(results: QueryResult[]): boolean {
    return results.every(result =>
      Array.isArray(result.data) &&
      result.metadata?.queryType?.includes('mindex')
    )
  }

  /**
   * 检查是否需要降级查询
   */
  private shouldFallbackToSingleQueries(
    infos: CheckDeriveOtherIndiceInfoPatched[],
    error: any
  ): boolean {
    // 如果指标数量很多，或者是特定类型的错误，建议降级
    if (infos.length > 10) {
      return true
    }

    // 检查错误类型
    const errorMessage = error.message || ''
    const shouldFallback = [
      'timeout',
      'too many',
      'limit exceeded',
      'memory'
    ].some(keyword => errorMessage.toLowerCase().includes(keyword))

    return shouldFallback
  }

  /**
   * 获取mindex查询统计信息
   */
  getMindexStatistics(
    infos: CheckDeriveOtherIndiceInfoPatched[],
    results: QueryResult[]
  ): {
    totalMetrics: number
    successfulQueries: number
    failedQueries: number
    batchQueries: number
    singleQueries: number
    averageExecutionTime: number
  } {
    const successfulQueries = results.filter(r =>
      !r.metadata?.error && r.data.length >= 0
    ).length

    const failedQueries = results.length - successfulQueries

    const batchQueries = results.filter(r =>
      r.metadata?.queryType === 'mindex_batch'
    ).length

    const singleQueries = results.filter(r =>
      r.metadata?.queryType === 'mindex_single'
    ).length

    const totalExecutionTime = results.reduce((sum, r) =>
      sum + (r.performance?.executionTime || 0), 0
    )

    const averageExecutionTime = results.length > 0 ?
      totalExecutionTime / results.length : 0

    return {
      totalMetrics: infos.length,
      successfulQueries,
      failedQueries,
      batchQueries,
      singleQueries,
      averageExecutionTime
    }
  }

  /**
   * 监控查询性能
   */
  monitorQueryPerformance(result: QueryResult): void {
    const { performance, metadata } = result

    if (!performance) return

    // 记录性能指标
    if (performance.executionTime > 5000) { // 超过5秒
      console.warn(`[MindexQueryHandler] 慢查询警告: ${metadata?.queryType}, 耗时: ${performance.executionTime}ms`)
    }

    if (performance.rowCount > 10000) { // 超过1万行
      console.warn(`[MindexQueryHandler] 大结果集警告: ${metadata?.queryType}, 行数: ${performance.rowCount}`)
    }
  }
}

// 导出处理器实例
export const mindexQueryHandler = new MindexQueryHandler()
