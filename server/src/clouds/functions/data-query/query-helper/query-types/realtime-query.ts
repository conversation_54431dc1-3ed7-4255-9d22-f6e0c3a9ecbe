/**
 * 指标查询重构 - 实时指标查询处理器
 * 
 * 负责：
 * 1. 实时指标查询逻辑
 * 2. 复合指标处理（纯实时、混合）
 * 3. 数据源分组优化
 * 4. CTE和Arquero优化保持
 */

import _ from 'lodash'
import { mapAwaitAll } from '@utils/util'
import { 
  QueryHandler, 
  QueryContext, 
  QueryResult, 
  QueryType,
  DataSourceGroup
} from '../query-interfaces'
import { CheckDeriveOtherIndiceInfoPatched, fetchSql, getSafeFilters } from '../data-loader'
import { DEFAULT_LIMIT, isAggCol } from '../sql-builder'
import { DataSourceQueryConfig, ColumnInfo } from '@/types/data-source'
import { IndicesBase } from '@/types/indices-table'

// 导入现有的函数以保持兼容性
import { 
  queryRealtimeIndicesTable, 
  getCompositeFormulaWithCode,
  resolveComplexColumn 
} from '../../realtime-indices'

/**
 * 实时指标查询处理器
 * 
 * 设计原则：
 * 1. 完全保留现有的实时指标查询逻辑
 * 2. 增强数据源分组优化
 * 3. 支持复合指标的完整处理
 * 4. 保持所有性能优化
 */
export class RealtimeQueryHandler implements QueryHandler {
  
  /**
   * 判断是否可以处理给定的指标信息
   */
  canHandle(infos: CheckDeriveOtherIndiceInfoPatched[]): boolean {
    return infos.some(info => 
      // 实时指标
      info.sqlModelId === 'realtime' ||
      // 复合指标（包含实时复合指标）
      info.sqlModelId?.startsWith('complex_') ||
      // 没有sqlModelId但有baseInfo且isLanding为false的实时指标
      (!info.sqlModelId && info.baseInfo && !info.baseInfo.isLanding)
    )
  }

  /**
   * 获取查询类型标识
   */
  getQueryType(): string {
    return QueryType.REALTIME
  }

  /**
   * 获取处理器优先级
   */
  getPriority(): number {
    return 100 // 高优先级，优先处理实时指标
  }

  /**
   * 执行查询
   */
  async execute(context: QueryContext): Promise<QueryResult[]> {
    const { queryConfig, req, infos, startTime } = context
    
    try {
      // 1. 检测复合指标类型
      const compositeInfo = infos.find(info => info.composedMetricInfos?.length > 0)
      
      if (compositeInfo) {
        if (this.isPureRealtimeComposite(compositeInfo)) {
          // 纯实时复合指标：使用优化的查询
          return this.handlePureRealtimeComposite(compositeInfo, context)
        } else {
          // 混合复合指标：使用递归查询
          return this.handleComplexComposite(compositeInfo, context)
        }
      }

      // 2. 普通实时指标处理
      return this.handleRegularRealtime(context)

    } catch (error) {
      console.error('[RealtimeQueryHandler] 查询执行失败:', error)
      throw error
    }
  }

  /**
   * 检测是否为纯实时复合指标
   * 
   * 完全保留原有的检测逻辑
   */
  private isPureRealtimeComposite(compositeInfo: CheckDeriveOtherIndiceInfoPatched): boolean {
    if (!compositeInfo.composedMetricInfos?.length) {
      return false
    }

    return compositeInfo.composedMetricInfos.every((subMetric: any) => {
      const baseInfo = subMetric.baseInfo
      const isRealtimeMetric = baseInfo && 
        baseInfo.isLanding === false && 
        baseInfo.type === 'base'
      const isNotNestedComposite = !subMetric.composedMetricInfos || 
        subMetric.composedMetricInfos.length === 0
      return isRealtimeMetric && isNotNestedComposite
    })
  }

  /**
   * 处理纯实时复合指标
   */
  private async handlePureRealtimeComposite(
    compositeInfo: CheckDeriveOtherIndiceInfoPatched,
    context: QueryContext
  ): Promise<QueryResult[]> {
    const { queryConfig, req } = context
    const startTime = Date.now()

    console.log('[RealtimeQueryHandler] 处理纯实时复合指标')

    // 使用现有的 queryRealtimeIndicesTable 逻辑
    const data = await queryRealtimeIndicesTable(queryConfig, req, [compositeInfo])

    const executionTime = Date.now() - startTime

    return [{
      data: data || [],
      metadata: {
        queryType: 'pure_realtime_composite',
        dataSource: compositeInfo.databaseId,
        duration: executionTime,
        fromCache: false
      },
      performance: {
        executionTime,
        rowCount: data?.length || 0,
        metricCount: 1,
        optimized: true
      }
    }]
  }

  /**
   * 处理混合复合指标
   */
  private async handleComplexComposite(
    compositeInfo: CheckDeriveOtherIndiceInfoPatched,
    context: QueryContext
  ): Promise<QueryResult[]> {
    const { queryConfig, req, db } = context
    const startTime = Date.now()

    console.log('[RealtimeQueryHandler] 处理混合复合指标')

    // 创建doQuery函数，保持与原有逻辑一致
    const doQuery = async (queryMod: (queryConfig: DataSourceQueryConfig) => DataSourceQueryConfig) => {
      const modifiedConfig = queryMod(queryConfig)
      // 这里需要递归调用查询分发器
      const { queryDispatcher } = await import('../query-dispatcher')
      return queryDispatcher.dispatch(modifiedConfig, req, db)
    }

    // 使用现有的 resolveComplexColumn 逻辑
    const data = await resolveComplexColumn(compositeInfo, doQuery)

    const executionTime = Date.now() - startTime

    return [{
      data: data || [],
      metadata: {
        queryType: 'complex_composite',
        dataSource: 'mixed',
        duration: executionTime,
        fromCache: false
      },
      performance: {
        executionTime,
        rowCount: data?.length || 0,
        metricCount: 1,
        optimized: false // 混合复合指标无法完全优化
      }
    }]
  }

  /**
   * 处理普通实时指标
   */
  private async handleRegularRealtime(context: QueryContext): Promise<QueryResult[]> {
    const { queryConfig, req, infos } = context
    const startTime = Date.now()

    console.log('[RealtimeQueryHandler] 处理普通实时指标')

    // 1. 按数据源分组优化
    const groups = this.groupByDataSource(infos)
    
    // 2. 并行执行各组查询
    const groupResults = await Promise.all(
      groups.map(group => this.executeGroupQuery(group, context))
    )

    // 3. 合并结果
    const allData = groupResults.flatMap(result => result.data)
    const totalExecutionTime = Date.now() - startTime

    return [{
      data: allData,
      metadata: {
        queryType: 'regular_realtime',
        dataSource: groups.map(g => g.databaseId).join(','),
        duration: totalExecutionTime,
        fromCache: false
      },
      performance: {
        executionTime: totalExecutionTime,
        rowCount: allData.length,
        metricCount: infos.length,
        optimized: groups.length < infos.length // 如果分组数少于指标数，说明有优化
      }
    }]
  }

  /**
   * 按数据源分组指标
   */
  private groupByDataSource(infos: CheckDeriveOtherIndiceInfoPatched[]): DataSourceGroup[] {
    const groups = _.groupBy(infos, info => info.databaseId || 'default')
    
    return Object.entries(groups).map(([databaseId, groupInfos]) => ({
      databaseId,
      infos: groupInfos,
      canUseCTE: groupInfos.length > 1, // 多个指标可以使用CTE优化
      groupType: 'realtime' as const
    }))
  }

  /**
   * 执行分组查询
   */
  private async executeGroupQuery(
    group: DataSourceGroup, 
    context: QueryContext
  ): Promise<QueryResult> {
    const { queryConfig, req } = context
    const startTime = Date.now()

    // 使用现有的 queryRealtimeIndicesTable 逻辑
    const data = await queryRealtimeIndicesTable(queryConfig, req, group.infos)

    const executionTime = Date.now() - startTime

    return {
      data: data || [],
      metadata: {
        queryType: 'realtime_group',
        dataSource: group.databaseId,
        duration: executionTime,
        fromCache: false
      },
      performance: {
        executionTime,
        rowCount: data?.length || 0,
        metricCount: group.infos.length,
        optimized: group.canUseCTE
      }
    }
  }

  /**
   * 验证查询结果
   */
  private validateResults(results: QueryResult[]): boolean {
    return results.every(result => 
      Array.isArray(result.data) && 
      typeof result.performance?.executionTime === 'number'
    )
  }
}

// 导出处理器实例
export const realtimeQueryHandler = new RealtimeQueryHandler()
